/**
 * Whop Proxy Endpoint
 * This endpoint receives requests from the main app and forwards them to the intermediary server
 * while preserving the x-whop-user-token header that the Whop proxy adds
 */

// Use production URL for both development and production
// Note: In Vercel serverless functions, VITE_ prefixed variables aren't available
const INTERMEDIARY_SERVER_URL = process.env.WHOP_INTERMEDIARY_URL ||
                                process.env.VITE_WHOP_INTERMEDIARY_URL ||
                                'https://whop-intermediary-server.vercel.app';

export default async function handler(req, res) {
  // Add detailed logging for debugging
  console.log('🔄 Whop Proxy Request:', {
    method: req.method,
    url: req.url,
    headers: Object.keys(req.headers),
    query: req.query
  });

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-whop-user-token');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    return res.status(200).end();
  }

  // Verify that we support the HTTP method
  const supportedMethods = ['GET', 'POST', 'PUT', 'DELETE'];
  if (!supportedMethods.includes(req.method)) {
    console.error('❌ Unsupported HTTP method:', req.method);
    return res.status(405).json({
      success: false,
      error: `Method ${req.method} not allowed`,
      supportedMethods
    });
  }

  try {
    // Extract the endpoint from the query parameter
    const { endpoint } = req.query;

    if (!endpoint) {
      console.error('❌ Missing endpoint parameter');
      return res.status(400).json({
        success: false,
        error: 'Missing endpoint parameter'
      });
    }

    console.log('📡 Forwarding to endpoint:', endpoint);

    // Handle health check endpoint locally for testing
    if (endpoint === 'health' || endpoint === 'whop/health') {
      console.log('🏥 Handling health check locally...');
      return res.status(200).json({
        success: true,
        status: 'healthy',
        timestamp: new Date().toISOString(),
        proxy: 'working',
        method: req.method
      });
    }

    // Handle charge endpoint directly if intermediary server is not available
    if (endpoint.includes('whop/charge')) {
      console.log('💳 Handling charge endpoint directly...');

      // Import the charge handler dynamically
      try {
        const chargeHandler = await import('./whop/charge.js');
        return await chargeHandler.default(req, res);
      } catch (importError) {
        console.error('❌ Failed to import charge handler:', importError);
        // Continue with proxy logic as fallback
      }
    }

    // Handle community analytics endpoint locally
    if (endpoint.includes('community-analytics')) {
      console.log('📊 Handling community analytics endpoint locally...');

      // Extract experienceId from the endpoint URL
      const urlParams = new URLSearchParams(endpoint.split('?')[1] || '');
      const experienceId = urlParams.get('experienceId');

      console.log('📊 Extracted experienceId:', experienceId);

      // Set the experienceId in req.query for the handler
      req.query = { ...req.query, experienceId };

      // Import the community analytics handler dynamically
      try {
        const analyticsHandler = await import('./whop/community-analytics.js');
        return await analyticsHandler.default(req, res);
      } catch (importError) {
        console.error('❌ Failed to import community analytics handler:', importError);
        // Return error response
        return res.status(500).json({
          success: false,
          error: 'Failed to load community analytics handler'
        });
      }
    }

    // Construct the full URL to the intermediary server
    const targetUrl = `${INTERMEDIARY_SERVER_URL}/api/${endpoint}`;

    console.log('🌐 Intermediary server configuration:', {
      INTERMEDIARY_SERVER_URL,
      targetUrl,
      endpoint
    });

    // Check if intermediary server URL is properly configured
    if (!INTERMEDIARY_SERVER_URL || INTERMEDIARY_SERVER_URL === 'undefined') {
      console.error('❌ Intermediary server URL not configured');
      return res.status(500).json({
        success: false,
        error: 'Intermediary server not configured',
        details: 'WHOP_INTERMEDIARY_URL environment variable is missing'
      });
    }
    
    // Prepare headers to forward
    const forwardHeaders = {
      'Content-Type': 'application/json',
    };

    // Forward the Whop user token if present
    if (req.headers['x-whop-user-token']) {
      forwardHeaders['x-whop-user-token'] = req.headers['x-whop-user-token'];
    }

    // Also forward other Whop-related headers that might be present
    const whopHeaders = [
      'x-whop-user-token',
      'x-whop-company-id',
      'x-whop-app-id',
      'x-whop-experience-id'
    ];

    whopHeaders.forEach(headerName => {
      if (req.headers[headerName]) {
        forwardHeaders[headerName] = req.headers[headerName];
      }
    });

    // Forward other relevant headers
    if (req.headers['authorization']) {
      forwardHeaders['authorization'] = req.headers['authorization'];
    }

    // Prepare the request options
    const requestOptions = {
      method: req.method,
      headers: forwardHeaders,
    };

    // Add body for POST/PUT requests
    if (req.method === 'POST' || req.method === 'PUT') {
      requestOptions.body = JSON.stringify(req.body);
      console.log('📤 Request body:', {
        originalBody: req.body,
        stringifiedBody: requestOptions.body
      });
    }



    // Make the request to the intermediary server
    console.log('🚀 Making request to intermediary server:', {
      url: targetUrl,
      method: requestOptions.method,
      headers: Object.keys(requestOptions.headers),
      hasBody: !!requestOptions.body
    });

    const response = await fetch(targetUrl, requestOptions);
    const data = await response.text();

    console.log('📥 Intermediary server response:', {
      status: response.status,
      statusText: response.statusText,
      contentType: response.headers.get('content-type'),
      dataLength: data.length,
      dataPreview: data.substring(0, 200)
    });

    // Forward the response
    res.status(response.status);
    res.setHeader('Content-Type', response.headers.get('content-type') || 'application/json');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.end(data);

  } catch (error) {
    console.error('❌ Proxy error:', {
      message: error.message,
      stack: error.stack,
      endpoint,
      method: req.method
    });

    res.status(500).json({
      success: false,
      error: 'Proxy error',
      message: error.message,
      endpoint,
      method: req.method
    });
  }
}
