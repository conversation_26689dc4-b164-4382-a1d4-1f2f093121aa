import { WhopServerSdk } from '@whop/api';

// Initialize Whop SDK (server-side only)
const whopSdk = WhopServerSdk({
  appId: process.env.VITE_WHOP_APP_ID,
  appApiKey: process.env.WHOP_API_KEY,
  onBehalfOfUserId: process.env.VITE_WHOP_AGENT_USER_ID,
  companyId: process.env.VITE_WHOP_COMPANY_ID,
});

export default async (req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, x-whop-user-token');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    const { recipientUsername, amount, experienceId, paymentMetadata } = req.body;



    // Validate input
    if (!recipientUsername || !amount || !experienceId) {
      return res.status(400).json({ 
        success: false, 
        error: 'Missing required fields: recipientUsername, amount, experienceId' 
      });
    }

    // Get experience details to find company info
    const experience = await whopSdk.experiences.getExperience({ experienceId });
    if (!experience) {
      throw new Error('Experience not found');
    }

    const companyId = experience.company.id;

    // Get company ledger account
    const ledgerAccount = await whopSdk.companies.getCompanyLedgerAccount({ companyId });
    if (!ledgerAccount?.company?.ledgerAccount?.id) {
      throw new Error('Company ledger account not found');
    }



    // Send payout to recipient
    const payoutResult = await whopSdk.payments.payUser({
      amount: amount,
      currency: "usd",
      destinationId: recipientUsername,
      ledgerAccountId: ledgerAccount.company.ledgerAccount.id,
      transferFee: ledgerAccount.company.ledgerAccount.transferFee,
    });



    // TODO: Record the payout in database (would need Supabase integration)
    // For now, we'll just return success

    res.json({
      success: true, 
      payoutId: payoutResult.id,
      amount: amount,
      recipient: recipientUsername
    });

  } catch (error) {
    
    res.status(500).json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    });
  }
};
