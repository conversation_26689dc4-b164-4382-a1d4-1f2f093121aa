import { WhopServerSdk } from '@whop/api';

// Initialize Whop SDK (server-side only)
const whopSdk = WhopServerSdk({
  appId: process.env.VITE_WHOP_APP_ID,
  appApiKey: process.env.WHOP_API_KEY,
  onBehalfOfUserId: process.env.VITE_WHOP_AGENT_USER_ID,
  companyId: process.env.VITE_WHOP_COMPANY_ID,
});

export default async (req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, x-whop-user-token');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    console.log('🔐 Getting current Whop user using getCurrentUser()...');

    // Use getCurrentUser() instead of extracting user ID from headers
    // This method automatically handles authentication based on the request context
    const user = await whopSdk.users.getCurrentUser();

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'Current user not found or not authenticated'
      });
    }

    console.log('✅ Current Whop user retrieved:', { userId: user.id, username: user.username });

    res.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        profilePicUrl: user.profilePicture?.url || user.profilePicUrl
      }
    });
  } catch (error) {
    console.error('❌ Failed to get current user:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get current user',
      message: error.message
    });
  }
};
