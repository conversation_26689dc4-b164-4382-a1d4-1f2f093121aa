import { WhopServerSdk } from '@whop/api';

// Initialize Whop SDK (server-side only)
const whopSdk = WhopServerSdk({
  appId: process.env.VITE_WHOP_APP_ID,
  appApiKey: process.env.WHOP_API_KEY,
  onBehalfOfUserId: process.env.VITE_WHOP_AGENT_USER_ID,
  companyId: process.env.VITE_WHOP_COMPANY_ID,
});

export default async (req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, x-whop-user-token');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    const { userId, experienceId } = req.body;

    if (!userId || !experienceId) {
      return res.status(400).json({ 
        success: false, 
        error: 'Missing userId or experienceId' 
      });
    }



    const result = await whopSdk.access.checkIfUserHasAccessToExperience({
      userId,
      experienceId,
    });



    res.json({
      success: true,
      access: {
        hasAccess: result.hasAccess,
        accessLevel: result.accessLevel,
        userId,
        experienceId
      }
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: 'Failed to check access',
      message: error.message 
    });
  }
};
