import { WhopServerSdk } from '@whop/api';
import { createClient } from '@supabase/supabase-js';

// Initialize Whop SDK (server-side only)
const whopSdk = WhopServerSdk({
  appId: process.env.VITE_WHOP_APP_ID,
  appApiKey: process.env.WHOP_API_KEY,
  onBehalfOfUserId: process.env.VITE_WHOP_AGENT_USER_ID,
  companyId: process.env.VITE_WHOP_COMPANY_ID,
});

// Initialize Supabase client (server-side only)
const supabaseUrl = process.env.SUPABASE_URL || 'https://pajqstbgncpbpcaffbpm.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY ||
                          process.env.VITE_SUPABASE_SERVICE_ROLE_KEY ||
                          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBhanFzdGJnbmNwYnBjYWZmYnBtIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTUxMDE4OCwiZXhwIjoyMDYxMDg2MTg4fQ.2vGgAdN8wsGQ_t0aIQ3Lmo-O6F6UFdQi4UweoAG9o-4';

// Debug environment variables
console.log('🔧 Supabase Environment Check:', {
  hasSupabaseUrl: !!supabaseUrl,
  supabaseUrl: supabaseUrl,
  hasServiceKey: !!supabaseServiceKey,
  serviceKeyPrefix: supabaseServiceKey ? supabaseServiceKey.substring(0, 20) + '...' : 'Not found',
  availableEnvVars: Object.keys(process.env).filter(key => key.includes('SUPABASE')),
});

const supabase = supabaseUrl && supabaseServiceKey ? createClient(supabaseUrl, supabaseServiceKey) : null;

/**
 * Get community analytics from the database
 */
async function getCommunityAnalyticsFromDB(experienceId) {
  try {
    // Check if Supabase is available
    if (!supabase) {
      console.warn('Supabase client not available, returning fallback data');
      return getFallbackAnalytics();
    }

    // Get total signups and earnings for this experience
    const { data: totalStats, error: totalError } = await supabase
      .from('affiliate_payouts')
      .select('amount')
      .eq('experience_id', experienceId)
      .eq('status', 'completed');

    if (totalError) {
      console.error('Error fetching total stats:', totalError);
      throw totalError;
    }

    const totalSignups = totalStats?.length || 0;
    const totalEarnings = totalStats?.reduce((sum, payout) => sum + parseFloat(payout.amount), 0) || 0;

    // Get recent signups (last 10)
    const { data: recentPayouts, error: recentError } = await supabase
      .from('affiliate_payouts')
      .select('recipient_username, amount, created_at')
      .eq('experience_id', experienceId)
      .eq('status', 'completed')
      .order('created_at', { ascending: false })
      .limit(10);

    if (recentError) {
      console.error('Error fetching recent signups:', recentError);
      throw recentError;
    }

    const recentSignups = recentPayouts?.map(payout => ({
      username: payout.recipient_username,
      signupDate: payout.created_at,
      amount: parseFloat(payout.amount)
    })) || [];

    // Get monthly stats (current month vs last month)
    const now = new Date();
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    // Current month stats
    const { data: currentMonthData, error: currentMonthError } = await supabase
      .from('affiliate_payouts')
      .select('amount')
      .eq('experience_id', experienceId)
      .eq('status', 'completed')
      .gte('created_at', currentMonthStart.toISOString());

    if (currentMonthError) {
      console.error('Error fetching current month stats:', currentMonthError);
    }

    // Last month stats
    const { data: lastMonthData, error: lastMonthError } = await supabase
      .from('affiliate_payouts')
      .select('amount')
      .eq('experience_id', experienceId)
      .eq('status', 'completed')
      .gte('created_at', lastMonthStart.toISOString())
      .lte('created_at', lastMonthEnd.toISOString());

    if (lastMonthError) {
      console.error('Error fetching last month stats:', lastMonthError);
    }

    const currentMonth = currentMonthData?.length || 0;
    const lastMonth = lastMonthData?.length || 0;
    const growth = lastMonth > 0 ? ((currentMonth - lastMonth) / lastMonth) * 100 : 0;

    return {
      totalSignups,
      totalEarnings: parseFloat(totalEarnings.toFixed(2)),
      recentSignups,
      monthlyStats: {
        currentMonth,
        lastMonth,
        growth: parseFloat(growth.toFixed(1))
      }
    };
  } catch (error) {
    console.error('Error in getCommunityAnalyticsFromDB:', error);
    // Return fallback data if database query fails
    return getFallbackAnalytics();
  }
}

/**
 * Get fallback analytics data when database is not available
 */
function getFallbackAnalytics() {
  console.warn('🚨 Using fallback analytics data - database not available');
  return {
    totalSignups: 0,
    totalEarnings: 0,
    recentSignups: [],
    monthlyStats: {
      currentMonth: 0,
      lastMonth: 0,
      growth: 0
    }
  };
}

export default async (req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, x-whop-user-token');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    const { experienceId } = req.query;

    if (!experienceId) {
      return res.status(400).json({ 
        success: false, 
        error: 'Missing experienceId parameter' 
      });
    }


    // Query real data from the affiliate_payouts table
    const analytics = await getCommunityAnalyticsFromDB(experienceId);

    console.log('📊 Community Analytics Retrieved:', {
      experienceId,
      totalSignups: analytics.totalSignups,
      totalEarnings: analytics.totalEarnings,
      recentSignupsCount: analytics.recentSignups.length
    });

    res.json({
      success: true,
      data: analytics
    });

  } catch (error) {
    
    res.status(500).json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    });
  }
};
