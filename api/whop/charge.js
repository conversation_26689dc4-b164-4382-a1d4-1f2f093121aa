import { WhopServerSdk } from '@whop/api';

// Initialize Whop SDK (server-side only)
const whopSdk = WhopServerSdk({
  appId: process.env.VITE_WHOP_APP_ID,
  appApiKey: process.env.WHOP_API_KEY,
  onBehalfOfUserId: process.env.VITE_WHOP_AGENT_USER_ID,
  companyId: process.env.VITE_WHOP_COMPANY_ID,
});

export default async (req, res) => {
  console.log('💳 Whop Charge Request:', {
    method: req.method,
    headers: Object.keys(req.headers),
    body: req.body
  });

  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, x-whop-user-token');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    console.error('❌ Invalid method for charge endpoint:', req.method);
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { amount, currency = 'usd', description = 'TradeOff Trading Competition Entry', metadata = {} } = req.body;

    console.log('💰 Processing charge:', { amount, currency, description, metadata });



    // Validate input
    if (!amount || amount <= 0) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid amount specified' 
      });
    }

    // Get user token from headers
    const userToken = req.headers['x-whop-user-token'];
    if (!userToken) {
      return res.status(401).json({ 
        success: false, 
        error: 'No user token provided' 
      });
    }

    // Verify user token and get user ID
    const { userId } = await whopSdk.verifyUserToken(userToken);
    if (!userId) {
      return res.status(401).json({ 
        success: false, 
        error: 'Invalid user token' 
      });
    }



    // Create charge with affiliate metadata
    const chargeMetadata = {
      ...metadata,
      userId,
      timestamp: new Date().toISOString(),
      source: 'trading_competition'
    };

    const result = await whopSdk.payments.chargeUser({
      amount: amount,
      currency: currency,
      userId: userId,
      metadata: chargeMetadata,
    });

    if (!result?.inAppPurchase) {
      throw new Error("Failed to create charge");
    }



    console.log('✅ Charge created successfully:', {
      inAppPurchaseId: result.inAppPurchase?.id,
      planId: result.inAppPurchase?.planId
    });

    console.log('✅ Charge created successfully:', {
      inAppPurchaseId: result.inAppPurchase?.id,
      planId: result.inAppPurchase?.planId
    });

    res.json({
      success: true,
      data: {
        inAppPurchase: result.inAppPurchase,
        status: 'needs_action'
      }
    });

  } catch (error) {
    console.error('❌ Charge creation failed:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });

    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create charge'
    });
  }
};
