import { WhopServerSdk } from '@whop/api';

// Initialize Whop SDK (server-side only)
const whopSdk = WhopServerSdk({
  appId: process.env.VITE_WHOP_APP_ID,
  appApiKey: process.env.WHOP_API_KEY,
  onBehalfOfUserId: process.env.VITE_WHOP_AGENT_USER_ID,
  companyId: process.env.VITE_WHOP_COMPANY_ID,
});

export default async (req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, x-whop-signature');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    const payload = req.body;
    
      event: payload.event,
      data: payload.data ? Object.keys(payload.data) : 'no data'
    });

    // Handle different webhook events
    const { event, data } = payload;

    switch (event) {
      case 'payment.completed':
        await handlePaymentCompleted(data);
        break;

      case 'user.access_granted':
        break;

      case 'user.access_revoked':
        break;

      default:
    }

    res.json({ 
      success: true, 
      event, 
      processed: true 
    });

  } catch (error) {
    
    res.status(500).json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Webhook processing failed' 
    });
  }
};

async function handlePaymentCompleted(paymentData) {
  try {

    const { metadata, amount, currency, userId, receiptId } = paymentData;

    // Check if this payment is eligible for affiliate payout
    if (!metadata?.isAffiliateEligible || !metadata?.experienceId) {
      return;
    }

    const { experienceId, affiliatePayoutAmount = 1 } = metadata;

      experienceId,
      affiliatePayoutAmount,
      originalAmount: amount,
      userId,
      receiptId
    });

    // Get experience details to find the owner
    const experience = await whopSdk.experiences.getExperience({ experienceId });
    if (!experience) {
      return;
    }

    // Get the experience owner's username
    const ownerId = experience.company.ownerId;
    const owner = await whopSdk.users.getUser({ userId: ownerId });
    
    if (!owner) {
      return;
    }


    // Get company ledger account
    const companyId = experience.company.id;
    const ledgerAccount = await whopSdk.companies.getCompanyLedgerAccount({ companyId });
    
    if (!ledgerAccount?.company?.ledgerAccount?.id) {
      return;
    }

    // Send affiliate payout
    const payoutResult = await whopSdk.payments.payUser({
      amount: affiliatePayoutAmount,
      currency: currency || 'usd',
      destinationId: owner.username,
      ledgerAccountId: ledgerAccount.company.ledgerAccount.id,
      transferFee: ledgerAccount.company.ledgerAccount.transferFee,
    });

      payoutId: payoutResult.id,
      recipient: owner.username,
      amount: affiliatePayoutAmount,
      originalPayment: receiptId
    });

    // TODO: Record the payout in database for tracking
    // This would typically be done via Supabase or another database

  } catch (error) {
    // Don't throw error to avoid webhook retry loops
  }
}
