import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Middleware to handle API routes in development
export function apiMiddleware() {
  return {
    name: 'api-middleware',
    configureServer(server) {
      server.middlewares.use('/api', async (req, res, next) => {
        // Handle /api/whop routes and /api/charge
        if (!req.url.startsWith('/whop/') && !req.url.startsWith('/charge')) {
          return next();
        }

        try {
          // Extract the route path (e.g., '/whop/current-user' or '/charge')
          const routePath = req.url.split('?')[0]; // Remove query params

          // Map /charge to /whop/charge for file system
          const filePath = routePath === '/charge' ? '/whop/charge' : routePath;
          const apiPath = join(__dirname, 'api', filePath + '.js');

          // Check if the API file exists
          if (!fs.existsSync(apiPath)) {
            res.statusCode = 404;
            res.end(JSON.stringify({ error: 'API route not found' }));
            return;
          }

          // Parse request body for POST requests
          if (req.method === 'POST' && !req.body) {
            let body = '';
            req.on('data', chunk => {
              body += chunk.toString();
            });
            await new Promise(resolve => {
              req.on('end', () => {
                try {
                  req.body = body ? JSON.parse(body) : {};
                } catch (e) {
                  req.body = body;
                }
                resolve();
              });
            });
          }

          // Import and execute the API handler
          const { default: handler } = await import(apiPath + '?t=' + Date.now());
          
          // Create a mock response object with Vercel-like methods
          const mockRes = {
            statusCode: 200,
            headers: {},
            setHeader(name, value) {
              this.headers[name] = value;
            },
            status(code) {
              this.statusCode = code;
              return this;
            },
            json(data) {
              this.setHeader('Content-Type', 'application/json');
              res.statusCode = this.statusCode;
              Object.entries(this.headers).forEach(([key, value]) => {
                res.setHeader(key, value);
              });
              res.end(JSON.stringify(data));
            },
            end(data) {
              res.statusCode = this.statusCode;
              Object.entries(this.headers).forEach(([key, value]) => {
                res.setHeader(key, value);
              });
              res.end(data);
            }
          };

          // Execute the handler
          await handler(req, mockRes);
        } catch (error) {
          res.statusCode = 500;
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify({ 
            error: 'Internal Server Error', 
            message: error.message 
          }));
        }
      });
    }
  };
}
