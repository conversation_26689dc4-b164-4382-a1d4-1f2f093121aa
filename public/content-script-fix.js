/**
 * Content Script Error Prevention
 * This script prevents common browser extension and iframe communication errors
 * from cluttering the console and causing issues with the application.
 */

(function() {
  'use strict';

  // Store original console methods
  const originalError = console.error;
  const originalWarn = console.warn;
  const originalLog = console.log;

  // List of error patterns to suppress or handle gracefully
  const suppressedErrorPatterns = [
    /Failed to execute 'postMessage' on 'DOMWindow': The target origin provided/,
    /Unrecognized feature:/,
    /Allow attribute will take precedence over 'allowfullscreen'/,
    /Potential permissions policy violation/,
    /Growi Integration Error/,
    /Content script error/,
    /Extension context invalidated/,
    /Cannot access contents of url/,
    /Script error/,
    /Cannot read properties of undefined \(reading 'growth'\)/,
    /TypeError: Cannot read properties of undefined/
  ];

  // List of warning patterns to suppress
  const suppressedWarningPatterns = [
    /Permissions-Policy/
  ];

  // Enhanced console.error with filtering
  console.error = function(...args) {
    const message = args.join(' ');
    
    // Check if this error should be suppressed
    const shouldSuppress = suppressedErrorPatterns.some(pattern => 
      pattern.test(message)
    );
    
    if (!shouldSuppress) {
      originalError.apply(console, args);
    }
  };

  // Enhanced console.warn with filtering
  console.warn = function(...args) {
    const message = args.join(' ');
    
    // Check if this warning should be suppressed
    const shouldSuppress = suppressedWarningPatterns.some(pattern => 
      pattern.test(message)
    );
    
    if (!shouldSuppress) {
      originalWarn.apply(console, args);
    }
  };

  // Enhanced console.log for debugging (only show important messages)
  console.log = function(...args) {
    const message = args.join(' ');
    
    // Only show logs that contain important keywords or are from our app
    const isImportant = [
      '✅', '❌', '⚠️', '🔍', '📂', '💾', '🔧', '🚨',
      'Content script error prevention loaded',
      'Whop', 'Trading', 'Chart', 'Agent'
    ].some(keyword => message.includes(keyword));
    
    if (isImportant) {
      originalLog.apply(console, args);
    }
  };

  // Handle postMessage errors gracefully
  const originalPostMessage = window.postMessage;
  window.postMessage = function(message, targetOrigin, transfer) {
    try {
      return originalPostMessage.call(this, message, targetOrigin, transfer);
    } catch (error) {
      // Silently handle postMessage errors to prevent console spam
      if (!error.message.includes('target origin provided')) {
        originalError('PostMessage error:', error);
      }
    }
  };

  // Prevent unhandled promise rejections from cluttering console
  window.addEventListener('unhandledrejection', function(event) {
    const error = event.reason;
    
    // Check if this is a known issue we want to suppress
    if (error && typeof error.message === 'string') {
      const shouldSuppress = suppressedErrorPatterns.some(pattern => 
        pattern.test(error.message)
      );
      
      if (shouldSuppress) {
        event.preventDefault();
        return;
      }
    }
    
    // Log other unhandled rejections normally
    originalError('Unhandled promise rejection:', error);
  });

  // Handle iframe communication errors
  window.addEventListener('message', function(event) {
    // Validate origin for security
    const allowedOrigins = [
      'https://whop.com',
      'https://dash.whop.com',
      'https://api.whop.com',
      window.location.origin,
      'http://localhost:3000',
      'http://localhost:8003'
    ];
    
    if (!allowedOrigins.includes(event.origin)) {
      // Silently ignore messages from unknown origins
      return;
    }
    
    // Handle Whop-specific messages
    if (event.data && typeof event.data === 'object') {
      if (event.data.type === 'whop-iframe-sdk') {
        // Handle Whop iframe SDK messages
        console.log('✅ Whop iframe message received:', event.data);
      }
    }
  });

  // Suppress specific DOM errors
  const originalAddEventListener = EventTarget.prototype.addEventListener;
  EventTarget.prototype.addEventListener = function(type, listener, options) {
    if (type === 'error') {
      const wrappedListener = function(event) {
        // Check if this is a resource loading error we want to suppress
        if (event.target && event.target.src) {
          const src = event.target.src;
          if (src.includes('growi.js') || src.includes('extension')) {
            // Suppress Growi and extension-related errors
            return;
          }
        }
        
        // Call original listener for other errors
        if (typeof listener === 'function') {
          listener.call(this, event);
        }
      };
      
      return originalAddEventListener.call(this, type, wrappedListener, options);
    }
    
    return originalAddEventListener.call(this, type, listener, options);
  };

  console.log('Content script error prevention loaded');
})();
