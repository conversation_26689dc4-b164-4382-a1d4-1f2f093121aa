import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { apiMiddleware } from "./vite-api-middleware.js";
import { whopProxyMiddleware } from "./vite-whop-middleware.js";
// Remove the component tagger import since we're not using it
// import { componentTagger } from "lovable-tagger";

export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory.
  const env = loadEnv(mode, process.cwd(), '');
  const isProduction = mode === 'production';

  return {
    server: {
      host: "::",
      port: 8080
    },
    plugins: [
      react({
        // Enable React Fast Refresh for better development experience
        fastRefresh: true,
      }),
      apiMiddleware(),
      whopProxyMiddleware()
    ],
    define: {
      // Map Vite environment variables to Next.js style for Whop SDK compatibility
      'process.env.NEXT_PUBLIC_WHOP_APP_ID': JSON.stringify(env.VITE_WHOP_APP_ID),
      'import.meta.env.NEXT_PUBLIC_WHOP_APP_ID': JSON.stringify(env.VITE_WHOP_APP_ID),

      // Explicitly make Vite replace env variables for Whop
      'process.env.WHOP_CLIENT_ID': JSON.stringify(env.WHOP_CLIENT_ID),
      'process.env.WHOP_CLIENT_SECRET': JSON.stringify(env.WHOP_CLIENT_SECRET),
      'process.env.WHOP_REDIRECT_URI': JSON.stringify(env.WHOP_REDIRECT_URI),
      'process.env.WHOP_API_KEY': JSON.stringify(env.WHOP_API_KEY),
      'process.env.VITE_WHOP_APP_ID': JSON.stringify(env.VITE_WHOP_APP_ID),
      'process.env.VITE_WHOP_AGENT_USER_ID': JSON.stringify(env.VITE_WHOP_AGENT_USER_ID),
      'process.env.VITE_WHOP_COMPANY_ID': JSON.stringify(env.VITE_WHOP_COMPANY_ID)
    },
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    build: {
      // Optimize build for performance
      target: 'es2020',
      minify: false, // Disable minification to remove obfuscation
      cssMinify: false, // Disable CSS minification to keep CSS readable
      sourcemap: true, // Always generate sourcemaps for debugging

      // Enable code splitting and chunk optimization
      rollupOptions: {
        output: {
          // Disable manual chunks to avoid 404 errors
          manualChunks: undefined,
        },
      },

      // Optimize chunk size warnings
      chunkSizeWarningLimit: 1000,
    },

    // Optimize dependencies
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@tanstack/react-query',
        '@supabase/supabase-js',
        'axios',
        'date-fns',
        'clsx',
        'tailwind-merge',
        'framer-motion',
        'lucide-react'
      ],
      exclude: [
        // Exclude large dependencies that should be loaded on demand
        'echarts',
        'lightweight-charts',
        'html2canvas',
        'dom-to-image'
      ]
    },

    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: ['./src/test/setup.ts'],
      css: false,
    }
  };
});
