// Self-improvement engine for Aura
// This module analyzes backtesting results to identify patterns in correct and incorrect predictions
// and provides specific recommendations for improving Aura's accuracy


// Types for self-improvement analysis
export interface PredictionAnalysis {
  signal: string;
  date: string;
  isCorrect: boolean;
  confidenceScore: number;
  bullishScore: number;
  bearishScore: number;
  marketStructure: string;
  priceChange: number;
  details?: any;
}

export interface CategoryPerformance {
  name: string;
  correctRate: number;
  totalSamples: number;
  averageConfidence: number;
  recommendation: string;
}

export interface FactorAnalysis {
  factor: string;
  description: string;
  correctPredictions: number;
  incorrectPredictions: number;
  accuracy: number;
  averageImpact: number;
  recommendation: string;
}

export interface ImprovementRecommendation {
  summary: string;
  overallAccuracy: number;
  marketStructurePerformance: Record<string, CategoryPerformance>;
  confidenceLevelPerformance: Record<string, CategoryPerformance>;
  signalTypePerformance: Record<string, CategoryPerformance>;
  keyFactors: FactorAnalysis[];
  topRecommendations: string[];
}

/**
 * Analyze backtesting results to identify patterns and generate improvement recommendations
 */
export function analyzeBacktestResults(backtestResults: any[]): ImprovementRecommendation {
  // Extract prediction analyses from backtest results
  const predictions: PredictionAnalysis[] = [];
  
  for (let i = 0; i < backtestResults.length - 1; i++) {
    const result = backtestResults[i];
    const nextResult = backtestResults[i + 1];
    
    // Skip NEUTRAL signals
    if (result.signal === 'NEUTRAL') continue;
    
    const currentPrice = result.price;
    const nextPrice = nextResult.price;
    const priceChange = ((nextPrice - currentPrice) / currentPrice) * 100;
    
    // Determine if the prediction was correct
    let isCorrect = false;
    if (result.signal === 'LONG' && priceChange > 0) {
      isCorrect = true;
    } else if (result.signal === 'SHORT' && priceChange < 0) {
      isCorrect = true;
    }
    
    predictions.push({
      signal: result.signal,
      date: result.date,
      isCorrect,
      confidenceScore: result.confidenceScore || 0,
      bullishScore: result.bullishScore || 0,
      bearishScore: result.bearishScore || 0,
      marketStructure: result.marketStructure || 'unknown',
      priceChange,
      details: result.details
    });
  }
  
  // Calculate overall accuracy
  const correctPredictions = predictions.filter(p => p.isCorrect).length;
  const overallAccuracy = predictions.length > 0 ? (correctPredictions / predictions.length) * 100 : 0;
  
  // Analyze performance by market structure
  const marketStructurePerformance = analyzeByCategory(predictions, 'marketStructure');
  
  // Analyze performance by confidence level
  const confidenceLevelPerformance = analyzeByConfidenceLevel(predictions);
  
  // Analyze performance by signal type
  const signalTypePerformance = analyzeByCategory(predictions, 'signal');
  
  // Analyze key factors that influenced predictions
  const keyFactors = analyzeKeyFactors(predictions);
  
  // Generate top recommendations
  const topRecommendations = generateTopRecommendations(
    marketStructurePerformance,
    confidenceLevelPerformance,
    signalTypePerformance,
    keyFactors,
    overallAccuracy
  );
  
  return {
    summary: generateSummary(overallAccuracy, predictions.length, topRecommendations),
    overallAccuracy,
    marketStructurePerformance,
    confidenceLevelPerformance,
    signalTypePerformance,
    keyFactors,
    topRecommendations
  };
}

/**
 * Analyze performance by a specific category (market structure, signal type, etc.)
 */
function analyzeByCategory(predictions: PredictionAnalysis[], categoryField: 'marketStructure' | 'signal'): Record<string, CategoryPerformance> {
  const categories: Record<string, CategoryPerformance> = {};
  
  // Group predictions by category
  const groupedPredictions: Record<string, PredictionAnalysis[]> = {};
  
  for (const prediction of predictions) {
    const categoryValue = prediction[categoryField];
    if (!groupedPredictions[categoryValue]) {
      groupedPredictions[categoryValue] = [];
    }
    groupedPredictions[categoryValue].push(prediction);
  }
  
  // Calculate performance metrics for each category
  for (const [category, categoryPredictions] of Object.entries(groupedPredictions)) {
    if (categoryPredictions.length < 3) continue; // Skip categories with too few samples
    
    const correctPredictions = categoryPredictions.filter(p => p.isCorrect).length;
    const correctRate = (correctPredictions / categoryPredictions.length) * 100;
    const avgConfidence = categoryPredictions.reduce((sum, p) => sum + p.confidenceScore, 0) / categoryPredictions.length;
    
    let recommendation = '';
    
    if (categoryField === 'marketStructure') {
      if (correctRate < 50) {
        recommendation = `Reduce weight of signals in ${category} market structure`;
      } else if (correctRate > 70) {
        recommendation = `Increase weight of signals in ${category} market structure`;
      } else {
        recommendation = `Fine-tune ${category} market structure detection criteria`;
      }
    } else if (categoryField === 'signal') {
      if (correctRate < 50) {
        recommendation = `Increase threshold for generating ${category} signals`;
      } else if (correctRate > 70) {
        recommendation = `Current threshold for ${category} signals is effective`;
      } else {
        recommendation = `Review criteria for ${category} signal generation`;
      }
    }
    
    categories[category] = {
      name: category,
      correctRate,
      totalSamples: categoryPredictions.length,
      averageConfidence: avgConfidence,
      recommendation
    };
  }
  
  return categories;
}

/**
 * Analyze performance by confidence level
 */
function analyzeByConfidenceLevel(predictions: PredictionAnalysis[]): Record<string, CategoryPerformance> {
  const confidenceLevels: Record<string, CategoryPerformance> = {};
  
  // Define confidence level ranges
  const ranges = [
    { name: 'very_low', min: 0, max: 60 },
    { name: 'low', min: 60, max: 70 },
    { name: 'medium', min: 70, max: 80 },
    { name: 'high', min: 80, max: 90 },
    { name: 'very_high', min: 90, max: 100 }
  ];
  
  // Group predictions by confidence level
  for (const range of ranges) {
    const rangePredictions = predictions.filter(
      p => p.confidenceScore >= range.min && p.confidenceScore < range.max
    );
    
    if (rangePredictions.length < 3) continue; // Skip ranges with too few samples
    
    const correctPredictions = rangePredictions.filter(p => p.isCorrect).length;
    const correctRate = (correctPredictions / rangePredictions.length) * 100;
    const avgConfidence = rangePredictions.reduce((sum, p) => sum + p.confidenceScore, 0) / rangePredictions.length;
    
    let recommendation = '';
    
    if (correctRate < 50) {
      recommendation = `Increase threshold for ${range.name} confidence level`;
    } else if (correctRate > 70) {
      recommendation = `Current threshold for ${range.name} confidence level is effective`;
    } else {
      recommendation = `Review criteria for ${range.name} confidence level`;
    }
    
    confidenceLevels[range.name] = {
      name: `${range.min}-${range.max}%`,
      correctRate,
      totalSamples: rangePredictions.length,
      averageConfidence: avgConfidence,
      recommendation
    };
  }
  
  return confidenceLevels;
}

/**
 * Analyze key factors that influenced predictions
 */
function analyzeKeyFactors(predictions: PredictionAnalysis[]): FactorAnalysis[] {
  // Define key factors to analyze
  const factors = [
    {
      name: 'bullish_bearish_ratio',
      description: 'Ratio between bullish and bearish scores',
      extractValue: (p: PredictionAnalysis) => Math.abs(p.bullishScore - p.bearishScore)
    },
    {
      name: 'confidence_score',
      description: 'Overall confidence score',
      extractValue: (p: PredictionAnalysis) => p.confidenceScore
    },
    {
      name: 'price_change_magnitude',
      description: 'Magnitude of price change',
      extractValue: (p: PredictionAnalysis) => Math.abs(p.priceChange)
    }
  ];
  
  const factorAnalyses: FactorAnalysis[] = [];
  
  for (const factor of factors) {
    const correctPredictions = predictions.filter(p => p.isCorrect);
    const incorrectPredictions = predictions.filter(p => !p.isCorrect);
    
    const avgValueCorrect = correctPredictions.reduce((sum, p) => sum + factor.extractValue(p), 0) / 
                           (correctPredictions.length || 1);
    const avgValueIncorrect = incorrectPredictions.reduce((sum, p) => sum + factor.extractValue(p), 0) / 
                             (incorrectPredictions.length || 1);
    
    const impact = avgValueCorrect - avgValueIncorrect;
    const normalizedImpact = impact / (Math.max(avgValueCorrect, avgValueIncorrect) || 1);
    
    let recommendation = '';
    
    if (factor.name === 'bullish_bearish_ratio') {
      if (normalizedImpact > 0.2) {
        recommendation = 'Increase the required difference between bullish and bearish scores for signal generation';
      } else if (normalizedImpact < -0.1) {
        recommendation = 'Decrease the required difference between bullish and bearish scores for signal generation';
      } else {
        recommendation = 'Current bullish/bearish score difference threshold is appropriate';
      }
    } else if (factor.name === 'confidence_score') {
      if (normalizedImpact > 0.2) {
        recommendation = 'Increase the minimum confidence score required for signal generation';
      } else if (normalizedImpact < -0.1) {
        recommendation = 'Review confidence score calculation methodology';
      } else {
        recommendation = 'Current confidence score threshold is appropriate';
      }
    } else if (factor.name === 'price_change_magnitude') {
      if (normalizedImpact > 0.2) {
        recommendation = 'System performs better with larger price movements; consider adding volatility filters';
      } else if (normalizedImpact < -0.1) {
        recommendation = 'System performs better with smaller price movements; consider reducing sensitivity';
      } else {
        recommendation = 'System handles different price movement magnitudes appropriately';
      }
    }
    
    factorAnalyses.push({
      factor: factor.name,
      description: factor.description,
      correctPredictions: correctPredictions.length,
      incorrectPredictions: incorrectPredictions.length,
      accuracy: (correctPredictions.length / (predictions.length || 1)) * 100,
      averageImpact: normalizedImpact,
      recommendation
    });
  }
  
  // Add detailed analysis of incorrect predictions
  const incorrectPredictions = predictions.filter(p => !p.isCorrect);
  
  // Analyze LONG signals that should have been SHORT
  const incorrectLongs = incorrectPredictions.filter(p => p.signal === 'LONG' && p.priceChange < 0);
  const avgBullishScoreIncorrectLongs = incorrectLongs.reduce((sum, p) => sum + p.bullishScore, 0) / (incorrectLongs.length || 1);
  const avgBearishScoreIncorrectLongs = incorrectLongs.reduce((sum, p) => sum + p.bearishScore, 0) / (incorrectLongs.length || 1);
  
  // Analyze SHORT signals that should have been LONG
  const incorrectShorts = incorrectPredictions.filter(p => p.signal === 'SHORT' && p.priceChange > 0);
  const avgBullishScoreIncorrectShorts = incorrectShorts.reduce((sum, p) => sum + p.bullishScore, 0) / (incorrectShorts.length || 1);
  const avgBearishScoreIncorrectShorts = incorrectShorts.reduce((sum, p) => sum + p.bearishScore, 0) / (incorrectShorts.length || 1);
  
  if (incorrectLongs.length > 3) {
    factorAnalyses.push({
      factor: 'incorrect_long_signals',
      description: 'LONG signals that should have been SHORT',
      correctPredictions: 0,
      incorrectPredictions: incorrectLongs.length,
      accuracy: 0,
      averageImpact: avgBullishScoreIncorrectLongs - avgBearishScoreIncorrectLongs,
      recommendation: avgBullishScoreIncorrectLongs - avgBearishScoreIncorrectLongs < 10 ?
        'Increase the required difference between bullish and bearish scores for LONG signals' :
        'Review bullish factor weights; some factors may be overvalued'
    });
  }
  
  if (incorrectShorts.length > 3) {
    factorAnalyses.push({
      factor: 'incorrect_short_signals',
      description: 'SHORT signals that should have been LONG',
      correctPredictions: 0,
      incorrectPredictions: incorrectShorts.length,
      accuracy: 0,
      averageImpact: avgBearishScoreIncorrectShorts - avgBullishScoreIncorrectShorts,
      recommendation: avgBearishScoreIncorrectShorts - avgBullishScoreIncorrectShorts < 10 ?
        'Increase the required difference between bearish and bullish scores for SHORT signals' :
        'Review bearish factor weights; some factors may be overvalued'
    });
  }
  
  return factorAnalyses;
}

/**
 * Generate top recommendations based on all analyses
 */
function generateTopRecommendations(
  marketStructurePerformance: Record<string, CategoryPerformance>,
  confidenceLevelPerformance: Record<string, CategoryPerformance>,
  signalTypePerformance: Record<string, CategoryPerformance>,
  keyFactors: FactorAnalysis[],
  overallAccuracy: number
): string[] {
  const recommendations: string[] = [];
  
  // Add recommendations based on market structure performance
  const worstMarketStructure = Object.values(marketStructurePerformance)
    .filter(p => p.totalSamples >= 5)
    .sort((a, b) => a.correctRate - b.correctRate)[0];
  
  if (worstMarketStructure && worstMarketStructure.correctRate < 50) {
    recommendations.push(
      `Improve ${worstMarketStructure.name} market structure detection (${worstMarketStructure.correctRate.toFixed(1)}% accuracy)`
    );
  }
  
  // Add recommendations based on signal type performance
  for (const signalType of Object.values(signalTypePerformance)) {
    if (signalType.correctRate < 50 && signalType.totalSamples >= 5) {
      recommendations.push(
        `Increase threshold for generating ${signalType.name} signals (${signalType.correctRate.toFixed(1)}% accuracy)`
      );
    }
  }
  
  // Add recommendations from key factors
  for (const factor of keyFactors) {
    if (factor.averageImpact > 0.2 || factor.averageImpact < -0.1) {
      recommendations.push(factor.recommendation);
    }
  }
  
  // Add general recommendations based on overall accuracy
  if (overallAccuracy < 50) {
    recommendations.push('Review the entire signal generation methodology; overall accuracy is below 50%');
  } else if (overallAccuracy < 60) {
    recommendations.push('Increase the required difference between bullish and bearish scores for all signals');
  }
  
  // Deduplicate recommendations
  const uniqueRecommendations = Array.from(new Set(recommendations));
  
  // Limit to top 5 recommendations
  return uniqueRecommendations.slice(0, 5);
}

/**
 * Generate a summary of the improvement recommendations
 */
function generateSummary(overallAccuracy: number, totalSamples: number, topRecommendations: string[]): string {
  let summary = `Aura achieved ${overallAccuracy.toFixed(1)}% accuracy across ${totalSamples} predictions. `;
  
  if (overallAccuracy >= 70) {
    summary += 'Performance is good, but can be further improved. ';
  } else if (overallAccuracy >= 55) {
    summary += 'Performance is moderate and requires some adjustments. ';
  } else {
    summary += 'Performance needs significant improvement. ';
  }
  
  if (topRecommendations.length > 0) {
    summary += 'Key recommendation: ' + topRecommendations[0];
  }
  
  return summary;
}
