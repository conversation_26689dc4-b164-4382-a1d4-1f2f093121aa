// Technical Analysis Functions

// Import advanced analysis modules
import { Candle, analyzeCandlestickPatterns } from './candlestick-patterns.ts';
import { analyzeChartPatterns } from './chart-patterns.ts';
import { calculateFibonacciRetracements, calculateFibonacciExtensions } from './fibonacci-tools.ts';
import { analyzeMarketStructure, calculateRiskManagement } from './market-structure.ts';
import { calculateStopLossTakeProfit } from './risk-management.ts';
import { analyzeMultipleTimeframes } from './multi-timeframe.ts';
import { calculateBollingerBandsWithSqueeze, detectBollingerSqueeze } from './indicators.ts';
import { analyzeRecentGaps } from './gap-analysis.ts';
import { identifyValueAreas } from './volume-profile.ts';
import { analyzeMarketContext } from './market-context.ts';
import { analyzePriceAction } from './price-action.ts';
import { recommendOptionsContracts, OptionsRecommendation } from './options-analysis.ts';
// Import the volatility regime classification function
import { classifyVolatilityRegime } from './market-context.ts';

// Format symbol for Polygon API
export function formatPolygonSymbol(symbol: string): string {
  // Remove any $ prefix if present
  symbol = symbol.replace(/^\$/, '');

  // Handle crypto symbols (e.g., BTC -> X:BTCUSD)
  if (/^(BTC|ETH|XRP|LTC|DOGE|SOL|ADA|DOT|AVAX|MATIC|LINK|UNI|SHIB)$/.test(symbol.toUpperCase())) {
    return `X:${symbol.toUpperCase()}USD`;
  }

  return symbol.toUpperCase();
}

// Fetch historical price data from Polygon API
export async function fetchHistoricalData(symbol: string, startDate: string, endDate: string, apiKey: string, timeframe: string = 'day') {
  try {
    const formattedSymbol = formatPolygonSymbol(symbol);

    // Handle special timeframes like 15-minute intervals
    let multiplier = 1;
    let timeframeUnit = timeframe;
    let adjustedStartDate = startDate;
    let adjustedEndDate = endDate;

    // Check if timeframe is in the format "15minute" or similar
    if (timeframe.includes('minute')) {
      const match = timeframe.match(/^(\d+)minute$/);
      if (match && match[1]) {
        multiplier = parseInt(match[1], 10);
        timeframeUnit = 'minute';

        // For intraday timeframes, we need to adjust the date range
        // If the start date is too far in the past, Polygon might not have minute data
        // or the response might be too large

        // Calculate the difference in days between start and end dates
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);
        const daysDifference = Math.ceil((endDateObj.getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24));

        // For minute data, limit to 7 days max to avoid huge responses
        if (daysDifference > 7) {
          // Adjust start date to be 7 days before end date
          const newStartDate = new Date(endDateObj);
          newStartDate.setDate(endDateObj.getDate() - 7);
          adjustedStartDate = newStartDate.toISOString().split('T')[0];
          console.log(`Adjusted start date for ${timeframe} data from ${startDate} to ${adjustedStartDate}`);
        }
      }
    }

    const url = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/range/${multiplier}/${timeframeUnit}/${adjustedStartDate}/${adjustedEndDate}?adjusted=true&sort=asc&limit=50000&apiKey=${apiKey}`;

    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (!data.results || !Array.isArray(data.results)) {
      return { symbol: formattedSymbol, data: [] };
    }

    // Map the data to the Candle interface format
    const candles: Candle[] = data.results.map((bar: any) => ({
      date: new Date(bar.t).toISOString().split('T')[0],
      open: bar.o,
      high: bar.h,
      low: bar.l,
      close: bar.c,
      volume: bar.v
    }));

    return {
      symbol: formattedSymbol,
      data: candles
    };
  } catch (error) {
    console.error(`Error fetching data for ${symbol}:`, error);
    return { symbol, data: [] };
  }
}

// Fetch the latest price for a symbol
export async function fetchLatestPrice(symbol: string, apiKey: string): Promise<number | null> {
  try {
    const formattedSymbol = formatPolygonSymbol(symbol);

    // Get current date and time
    const now = new Date();

    // Format date components
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const todayStr = `${year}-${month}-${day}`;

    // Format time components for logging
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    // First approach: Try to get the latest intraday price using the current day's data
    // This will get the most recent price, even if it's from 15 minutes ago (Polygon's delay)
    const intradayUrl = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/range/1/minute/${todayStr}/${todayStr}?adjusted=true&sort=desc&limit=1&apiKey=${apiKey}`;

    console.log(`Fetching latest intraday price for ${symbol} at ${hours}:${minutes}:${seconds}`);

    const intradayResponse = await fetch(intradayUrl);

    if (intradayResponse.ok) {
      const intradayData = await intradayResponse.json();

      if (intradayData.results && intradayData.results.length > 0) {
        console.log(`Found intraday price for ${symbol}: ${intradayData.results[0].c}`);
        return intradayData.results[0].c; // Return the most recent price
      }
    }

    // Second approach: Try to get the latest price from the current day
    const dailyUrl = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/range/1/day/${todayStr}/${todayStr}?adjusted=true&sort=desc&limit=1&apiKey=${apiKey}`;

    console.log(`Fetching latest daily price for ${symbol}`);

    const dailyResponse = await fetch(dailyUrl);

    if (dailyResponse.ok) {
      const dailyData = await dailyResponse.json();

      if (dailyData.results && dailyData.results.length > 0) {
        console.log(`Found daily price for ${symbol}: ${dailyData.results[0].c}`);
        return dailyData.results[0].c; // Return the current day's price
      }
    }

    // Third approach: Fall back to previous close if we can't get today's data
    const prevCloseUrl = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/prev?adjusted=true&apiKey=${apiKey}`;

    console.log(`Falling back to previous close for ${symbol}`);

    const prevCloseResponse = await fetch(prevCloseUrl);

    if (prevCloseResponse.ok) {
      const prevCloseData = await prevCloseResponse.json();

      if (prevCloseData.results && prevCloseData.results.length > 0) {
        console.log(`Using previous close for ${symbol}: ${prevCloseData.results[0].c}`);
        return prevCloseData.results[0].c; // Return the previous close price
      }
    }

    console.log(`Could not find any price data for ${symbol}`);
    return null;
  } catch (error) {
    console.error(`Error fetching latest price for ${symbol}:`, error);
    return null;
  }
}

// Calculate Simple Moving Average (SMA)
export function calculateSMA(data: any[], period: number, priceKey: string = 'close'): number[] {
  const sma: number[] = [];

  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      sma.push(NaN); // Not enough data yet
      continue;
    }

    let sum = 0;
    for (let j = 0; j < period; j++) {
      sum += data[i - j][priceKey];
    }

    sma.push(sum / period);
  }

  return sma;
}

// Calculate Exponential Moving Average (EMA)
export function calculateEMA(data: any[], period: number, priceKey: string = 'close'): number[] {
  const ema: number[] = [];
  const multiplier = 2 / (period + 1);

  // Start with SMA for the first EMA value
  let sum = 0;
  for (let i = 0; i < period; i++) {
    sum += data[i][priceKey];
  }

  ema.push(sum / period);

  // Calculate EMA for the rest of the data
  for (let i = period; i < data.length; i++) {
    const currentPrice = data[i][priceKey];
    const previousEMA = ema[ema.length - 1];
    const currentEMA = (currentPrice - previousEMA) * multiplier + previousEMA;
    ema.push(currentEMA);
  }

  // Pad the beginning with NaN values
  const padding = Array(period - 1).fill(NaN);
  return [...padding, ...ema];
}

// Calculate Relative Strength Index (RSI)
export function calculateRSI(data: any[], period: number = 14): number[] {
  const rsi: number[] = [];
  const gains: number[] = [];
  const losses: number[] = [];

  // Calculate price changes
  for (let i = 1; i < data.length; i++) {
    const change = data[i].close - data[i - 1].close;
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? Math.abs(change) : 0);
  }

  // Calculate initial average gain and loss
  let avgGain = 0;
  let avgLoss = 0;

  for (let i = 0; i < period; i++) {
    avgGain += gains[i];
    avgLoss += losses[i];
  }

  avgGain /= period;
  avgLoss /= period;

  // Calculate RSI
  for (let i = 0; i < data.length; i++) {
    if (i < period) {
      rsi.push(NaN); // Not enough data yet
      continue;
    }

    if (i > period) {
      // Use smoothed averages for subsequent values
      avgGain = ((avgGain * (period - 1)) + gains[i - 1]) / period;
      avgLoss = ((avgLoss * (period - 1)) + losses[i - 1]) / period;
    }

    const rs = avgGain / (avgLoss === 0 ? 0.001 : avgLoss); // Avoid division by zero
    const currentRSI = 100 - (100 / (1 + rs));
    rsi.push(currentRSI);
  }

  return rsi;
}

// Calculate MACD
export function calculateMACD(data: any[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9): any {
  // Calculate fast and slow EMAs
  const fastEMA = calculateEMA(data, fastPeriod);
  const slowEMA = calculateEMA(data, slowPeriod);

  // Calculate MACD line
  const macdLine: number[] = [];
  for (let i = 0; i < data.length; i++) {
    if (isNaN(fastEMA[i]) || isNaN(slowEMA[i])) {
      macdLine.push(NaN);
    } else {
      macdLine.push(fastEMA[i] - slowEMA[i]);
    }
  }

  // Calculate signal line (EMA of MACD line)
  const signalLine: number[] = [];
  let validMacdValues: number[] = [];

  for (let i = 0; i < macdLine.length; i++) {
    if (!isNaN(macdLine[i])) {
      validMacdValues.push(macdLine[i]);
    }

    if (validMacdValues.length < signalPeriod) {
      signalLine.push(NaN);
      continue;
    }

    if (validMacdValues.length === signalPeriod) {
      // Initial SMA for signal line
      signalLine.push(validMacdValues.reduce((sum, val) => sum + val, 0) / signalPeriod);
    } else {
      // EMA calculation for signal line
      const multiplier = 2 / (signalPeriod + 1);
      const previousSignal = signalLine[signalLine.length - 1];
      const currentSignal = (macdLine[i] - previousSignal) * multiplier + previousSignal;
      signalLine.push(currentSignal);
    }
  }

  // Calculate histogram (MACD line - signal line)
  const histogram: number[] = [];
  for (let i = 0; i < data.length; i++) {
    if (isNaN(macdLine[i]) || isNaN(signalLine[i])) {
      histogram.push(NaN);
    } else {
      histogram.push(macdLine[i] - signalLine[i]);
    }
  }

  return {
    macdLine,
    signalLine,
    histogram
  };
}

// Calculate Bollinger Bands
export function calculateBollingerBands(data: any[], period: number = 20, stdDev: number = 2): any {
  const sma = calculateSMA(data, period);
  const upperBand: number[] = [];
  const lowerBand: number[] = [];

  for (let i = 0; i < data.length; i++) {
    if (isNaN(sma[i])) {
      upperBand.push(NaN);
      lowerBand.push(NaN);
      continue;
    }

    // Calculate standard deviation
    let sum = 0;
    for (let j = 0; j < period; j++) {
      if (i - j < 0) break;
      sum += Math.pow(data[i - j].close - sma[i], 2);
    }

    const standardDeviation = Math.sqrt(sum / period);

    upperBand.push(sma[i] + (stdDev * standardDeviation));
    lowerBand.push(sma[i] - (stdDev * standardDeviation));
  }

  return {
    middle: sma,
    upper: upperBand,
    lower: lowerBand
  };
}

// Calculate Average True Range (ATR)
export function calculateATR(data: Candle[], period: number = 14): number[] {
  const trueRanges: number[] = [];
  const atrs: number[] = [];

  // Calculate True Range for each candle
  for (let i = 0; i < data.length; i++) {
    if (i === 0) {
      // For the first candle, TR is simply the high - low
      trueRanges.push(data[i].high - data[i].low);
    } else {
      // For subsequent candles, TR is the greatest of:
      // 1. Current High - Current Low
      // 2. |Current High - Previous Close|
      // 3. |Current Low - Previous Close|
      const highLow = data[i].high - data[i].low;
      const highPrevClose = Math.abs(data[i].high - data[i-1].close);
      const lowPrevClose = Math.abs(data[i].low - data[i-1].close);

      trueRanges.push(Math.max(highLow, highPrevClose, lowPrevClose));
    }
  }

  // Calculate ATR using simple moving average of true ranges
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      // Not enough data for the period yet
      atrs.push(NaN);
    } else {
      // Calculate average of the last 'period' true ranges
      let sum = 0;
      for (let j = 0; j < period; j++) {
        sum += trueRanges[i - j];
      }
      atrs.push(sum / period);
    }
  }

  return atrs;
}



// Calculate Fibonacci Retracement Levels
export function calculateFibonacciLevels(data: any[]): any {
  if (data.length < 2) return null;

  // Find highest high and lowest low in the data
  let highestHigh = -Infinity;
  let lowestLow = Infinity;
  let highestIndex = 0;
  let lowestIndex = 0;

  for (let i = 0; i < data.length; i++) {
    if (data[i].high > highestHigh) {
      highestHigh = data[i].high;
      highestIndex = i;
    }

    if (data[i].low < lowestLow) {
      lowestLow = data[i].low;
      lowestIndex = i;
    }
  }

  // Determine if we're in an uptrend or downtrend
  const isUptrend = highestIndex > lowestIndex;

  // Calculate the range
  const range = highestHigh - lowestLow;

  // Calculate Fibonacci levels
  const levels = {
    trend: isUptrend ? 'uptrend' : 'downtrend',
    high: highestHigh,
    low: lowestLow,
    levels: {
      '0.0': isUptrend ? lowestLow : highestHigh,
      '0.236': isUptrend ? lowestLow + (range * 0.236) : highestHigh - (range * 0.236),
      '0.382': isUptrend ? lowestLow + (range * 0.382) : highestHigh - (range * 0.382),
      '0.5': isUptrend ? lowestLow + (range * 0.5) : highestHigh - (range * 0.5),
      '0.618': isUptrend ? lowestLow + (range * 0.618) : highestHigh - (range * 0.618),
      '0.786': isUptrend ? lowestLow + (range * 0.786) : highestHigh - (range * 0.786),
      '1.0': isUptrend ? highestHigh : lowestLow
    }
  };

  return levels;
}

// Find Support and Resistance Levels
export function findSupportResistance(data: any[], lookback: number = 5): any {
  if (data.length < lookback * 2) return { support: [], resistance: [] };

  const supports: number[] = [];
  const resistances: number[] = [];

  // Find local minima and maxima
  for (let i = lookback; i < data.length - lookback; i++) {
    let isMin = true;
    let isMax = true;

    for (let j = i - lookback; j <= i + lookback; j++) {
      if (j === i) continue;

      if (data[j].low <= data[i].low) isMin = false;
      if (data[j].high >= data[i].high) isMax = false;
    }

    if (isMin) supports.push(data[i].low);
    if (isMax) resistances.push(data[i].high);
  }

  // Group similar levels (within 2% of each other)
  const groupedSupports = groupSimilarLevels(supports);
  const groupedResistances = groupSimilarLevels(resistances);

  return {
    support: groupedSupports,
    resistance: groupedResistances
  };
}

// Helper function to group similar price levels
function groupSimilarLevels(levels: number[], tolerance: number = 0.02): number[] {
  if (levels.length === 0) return [];

  // Sort levels
  levels.sort((a, b) => a - b);

  const grouped: number[] = [];
  let currentGroup: number[] = [levels[0]];

  for (let i = 1; i < levels.length; i++) {
    const currentLevel = levels[i];
    const previousLevel = currentGroup[currentGroup.length - 1];

    // Check if the current level is within tolerance of the previous level
    if ((currentLevel - previousLevel) / previousLevel <= tolerance) {
      currentGroup.push(currentLevel);
    } else {
      // Calculate the average of the current group
      const average = currentGroup.reduce((sum, val) => sum + val, 0) / currentGroup.length;
      grouped.push(average);

      // Start a new group
      currentGroup = [currentLevel];
    }
  }

  // Add the last group
  if (currentGroup.length > 0) {
    const average = currentGroup.reduce((sum, val) => sum + val, 0) / currentGroup.length;
    grouped.push(average);
  }

  return grouped;
}

// Generate technical analysis for a symbol
export async function generateTechnicalAnalysis(symbol: string, data: Candle[], timeframe: string = 'day', apiKey?: string): Promise<any> {
  if (!data || data.length === 0) {
    return {
      symbol,
      error: "Insufficient data for analysis"
    };
  }

  // Calculate basic indicators
  const sma20 = calculateSMA(data, 20);
  const sma50 = calculateSMA(data, 50);
  const sma200 = calculateSMA(data, 200);

  const ema12 = calculateEMA(data, 12);
  const ema26 = calculateEMA(data, 26);

  const rsi = calculateRSI(data);
  const macd = calculateMACD(data);
  const bollingerBands = calculateBollingerBandsWithSqueeze(data);
  const bollingerSqueeze = detectBollingerSqueeze(data);

  // Get the most recent values from the data
  const lastIndex = data.length - 1;
  let currentPrice = data[lastIndex].close;

  // Check if we're analyzing historical data or current data
  const lastCandleDate = new Date(data[lastIndex].date);
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Only fetch latest price if the last candle is from today or yesterday
  // This ensures we don't override historical data with current prices
  const isRecentData = (today.getTime() - lastCandleDate.getTime()) <= 86400000; // 24 hours in milliseconds

  // Try to fetch the latest price if API key is provided AND we're looking at recent data
  if (apiKey && isRecentData) {
    try {
      const latestPrice = await fetchLatestPrice(symbol, apiKey);
      if (latestPrice !== null) {
        // Use the latest price instead of the last candle's close
        currentPrice = latestPrice;
        console.log(`Using latest price for ${symbol}: ${currentPrice}`);
      }
    } catch (error) {
      console.error(`Error fetching latest price for ${symbol}:`, error);
      // Continue with the last candle's close price
    }
  } else if (!isRecentData) {
    console.log(`Using historical close price for ${symbol} (${lastCandleDate.toISOString().split('T')[0]}): ${currentPrice}`);
  }

  // Advanced analysis
  // 1. Candlestick patterns and anatomy
  const candlestickPatterns = analyzeCandlestickPatterns(data);

  // 2. Price action analysis (Al Brooks inspired)
  const priceActionAnalysis = analyzePriceAction(data);
  const candleAnatomyData = priceActionAnalysis.recentCandles;

  // 3. Chart patterns
  const chartPatterns = analyzeChartPatterns(data);

  // 4. Market context and environment
  const marketContextAnalysis = analyzeMarketContext(data);

  // 5. Fibonacci analysis
  const fibonacciAnalysis = {
    retracements: calculateFibonacciRetracements(data),
    extensions: calculateFibonacciExtensions(data, data.length - 1),
    clusters: [] // TODO: Implement Fibonacci clusters
  };

  // 6. Market structure analysis
  const marketStructure = analyzeMarketStructure(data);

  // 7. Gap analysis
  const gapAnalysis = analyzeRecentGaps(data);

  // 8. Volume profile analysis
  const volumeProfileAnalysis = identifyValueAreas(data);

  // 9. Risk management calculations
  let signal = "NEUTRAL";

  // Determine trend based on moving averages
  let trend = "neutral";
  if (
    !isNaN(sma20[lastIndex]) &&
    !isNaN(sma50[lastIndex]) &&
    !isNaN(sma200[lastIndex])
  ) {
    if (
      currentPrice > sma20[lastIndex] &&
      sma20[lastIndex] > sma50[lastIndex] &&
      sma50[lastIndex] > sma200[lastIndex]
    ) {
      trend = "strong bullish";
    } else if (
      currentPrice > sma20[lastIndex] &&
      currentPrice > sma50[lastIndex]
    ) {
      trend = "bullish";
    } else if (
      currentPrice < sma20[lastIndex] &&
      sma20[lastIndex] < sma50[lastIndex] &&
      sma50[lastIndex] < sma200[lastIndex]
    ) {
      trend = "strong bearish";
    } else if (
      currentPrice < sma20[lastIndex] &&
      currentPrice < sma50[lastIndex]
    ) {
      trend = "bearish";
    }
  }

  // Determine RSI condition
  let rsiCondition = "neutral";
  if (!isNaN(rsi[lastIndex])) {
    if (rsi[lastIndex] > 70) {
      rsiCondition = "overbought";
    } else if (rsi[lastIndex] < 30) {
      rsiCondition = "oversold";
    }
  }

  // Determine MACD condition
  let macdCondition = "neutral";
  if (
    !isNaN(macd.macdLine[lastIndex]) &&
    !isNaN(macd.signalLine[lastIndex])
  ) {
    if (
      macd.macdLine[lastIndex] > macd.signalLine[lastIndex] &&
      macd.macdLine[lastIndex - 1] <= macd.signalLine[lastIndex - 1]
    ) {
      macdCondition = "bullish crossover";
    } else if (
      macd.macdLine[lastIndex] < macd.signalLine[lastIndex] &&
      macd.macdLine[lastIndex - 1] >= macd.signalLine[lastIndex - 1]
    ) {
      macdCondition = "bearish crossover";
    } else if (macd.macdLine[lastIndex] > macd.signalLine[lastIndex]) {
      macdCondition = "bullish";
    } else if (macd.macdLine[lastIndex] < macd.signalLine[lastIndex]) {
      macdCondition = "bearish";
    }
  }

  // Determine Bollinger Bands condition
  let bbCondition = "neutral";
  if (
    !isNaN(bollingerBands.bollingerBands.upper[lastIndex]) &&
    !isNaN(bollingerBands.bollingerBands.lower[lastIndex])
  ) {
    if (currentPrice > bollingerBands.bollingerBands.upper[lastIndex]) {
      bbCondition = "overbought";
    } else if (currentPrice < bollingerBands.bollingerBands.lower[lastIndex]) {
      bbCondition = "oversold";
    } else if (bollingerSqueeze.isSqueezing) {
      bbCondition = "squeeze";
    }
  }

  // Generate overall signal
  let confidence = "MEDIUM";

  // Count bullish and bearish indicators
  let bullishCount = 0;
  let bearishCount = 0;

  if (trend === "strong bullish" || trend === "bullish") bullishCount++;
  if (trend === "strong bearish" || trend === "bearish") bearishCount++;

  if (rsiCondition === "oversold") bullishCount++;
  if (rsiCondition === "overbought") bearishCount++;

  if (macdCondition === "bullish crossover" || macdCondition === "bullish") bullishCount++;
  if (macdCondition === "bearish crossover" || macdCondition === "bearish") bearishCount++;

  if (bbCondition === "oversold") bullishCount++;
  if (bbCondition === "overbought") bearishCount++;

  // Add candlestick pattern signals
  // Initialize candlePatterns with an empty array
  let candlePatterns: any[] = [];

  try {
    // Make sure candlestickPatterns is an array before filtering
    if (Array.isArray(candlestickPatterns)) {
      candlePatterns = candlestickPatterns.filter(p => p && typeof p.position === 'number' && p.position >= data.length - 3);
      for (const pattern of candlePatterns) {
        if (pattern.bullish && pattern.significance !== 'weak') bullishCount++;
        if (!pattern.bullish && pattern.significance !== 'weak') bearishCount++;
      }
    }
  } catch (error) {
    console.error('Error processing candlestick patterns:', error);
    // Continue with analysis even if pattern detection fails
  }

  // Add chart pattern signals
  for (const pattern of chartPatterns) {
    if (pattern.endIndex >= data.length - 5) {
      if (pattern.bullish) bullishCount++;
      else bearishCount++;
    }
  }

  // 10. Multi-timeframe analysis
  const multiTimeframeAnalysis = {
    timeframes: ["daily", "weekly", "monthly"],
    trends: {
      daily: trend,
      weekly: trend.includes("bullish") ? "bullish" : trend.includes("bearish") ? "bearish" : "neutral",
      monthly: trend.includes("bullish") ? "bullish" : trend.includes("bearish") ? "bearish" : "neutral"
    },
    conflictWarnings: [],
    dominantTrend: trend.includes("bullish") ? "bullish" : trend.includes("bearish") ? "bearish" : "neutral",
    nestedPatterns: trend.includes("bullish") ? ["nested_bullish_trend"] : trend.includes("bearish") ? ["nested_bearish_trend"] : [],
    confirmation: {
      bullish: trend.includes("bullish"),
      bearish: trend.includes("bearish"),
      neutral: trend === "neutral",
      strength: trend.includes("strong") ? "strong" : "moderate"
    }
  };

  // WEIGHTED SIGNAL GENERATION BASED ON AURA IMPORTANCE CHECKLIST
  // Initialize weighted scores
  let bullishScore = 0;
  let bearishScore = 0;
  const maxPossibleScore = 100; // Total of all weights

  // Track individual category scores for detailed reporting based on the Aura Importance Checklist
  const categoryScores = {
    marketStructure: { bullish: 0, bearish: 0, maxPossible: 30 },
    candleAnalysis: { bullish: 0, bearish: 0, maxPossible: 20 },
    trendlines: { bullish: 0, bearish: 0, maxPossible: 20 },
    supportResistance: { bullish: 0, bearish: 0, maxPossible: 15 },
    fibonacci: { bullish: 0, bearish: 0, maxPossible: 10 },
    indicators: { bullish: 0, bearish: 0, maxPossible: 5 }
  };

  // 1. MARKET STRUCTURE (30%)
  // 1.1 Trend identification (10%)
  if (marketStructure.structure === "uptrend") {
    bullishScore += 10;
    categoryScores.marketStructure.bullish += 10;
  } else if (marketStructure.structure === "downtrend") {
    bearishScore += 10;
    categoryScores.marketStructure.bearish += 10;
  } else if (marketStructure.structure === "range") {
    // In a range, check if price is near upper or lower boundary
    const rangeHigh = marketContextAnalysis.rangeHigh;
    const rangeLow = marketContextAnalysis.rangeLow;
    if (rangeHigh && rangeLow) {
      const rangeSize = rangeHigh - rangeLow;
      const positionInRange = (currentPrice - rangeLow) / rangeSize;

      if (positionInRange > 0.8) { // Near top of range
        bearishScore += 7;
        categoryScores.marketStructure.bearish += 7;
      } else if (positionInRange < 0.2) { // Near bottom of range
        bullishScore += 7;
        categoryScores.marketStructure.bullish += 7;
      }
    }
  }

  // 1.2 Range identification and boundaries (7%)
  if (marketContextAnalysis.environment === "range") {
    const rangeHigh = marketContextAnalysis.rangeHigh;
    const rangeLow = marketContextAnalysis.rangeLow;

    if (rangeHigh && rangeLow) {
      // If price is breaking out of range
      if (currentPrice > rangeHigh * 1.01) {
        bullishScore += 7;
        categoryScores.marketStructure.bullish += 7;
      } else if (currentPrice < rangeLow * 0.99) {
        bearishScore += 7;
        categoryScores.marketStructure.bearish += 7;
      }
    }
  }

  // 1.3 Channel formation and boundaries (6%)
  // Look for channel patterns in chart patterns
  const channelPatterns = chartPatterns.filter(p =>
    p.pattern.includes("channel") ||
    p.pattern.includes("flag") ||
    p.pattern.includes("wedge"));

  if (channelPatterns.length > 0) {
    // Use the most recent channel pattern
    const latestChannel = channelPatterns.reduce((latest, current) =>
      current.endIndex > latest.endIndex ? current : latest, channelPatterns[0]);

    if (latestChannel.bullish) {
      bullishScore += 6;
      categoryScores.marketStructure.bullish += 6;
    } else {
      bearishScore += 6;
      categoryScores.marketStructure.bearish += 6;
    }
  }

  // 1.4 Swing high/low breaks with close beyond previous extremes (7%)
  // Check for higher highs/lows or lower highs/lows
  if (marketStructure.higherHighs && marketStructure.higherLows) {
    bullishScore += 7;
    categoryScores.marketStructure.bullish += 7;
  } else if (marketStructure.lowerHighs && marketStructure.lowerLows) {
    bearishScore += 7;
    categoryScores.marketStructure.bearish += 7;
  }

  // 2. CANDLE ANALYSIS (20%)
  // 2.1 Candle close position relative to range (7%)
  const recentCandles = candleAnatomyData.slice(-3);
  for (const candle of recentCandles) {
    // Weight the most recent candle more heavily
    const weight = candle.position === data.length - 1 ? 0.6 : 0.2;

    if (candle.closeLocation === "near_high") {
      bullishScore += 7 * weight;
      categoryScores.candleAnalysis.bullish += 7 * weight;
    } else if (candle.closeLocation === "near_low") {
      bearishScore += 7 * weight;
      categoryScores.candleAnalysis.bearish += 7 * weight;
    }
  }

  // 2.2 Candle type significance (5%)
  // Check for significant candlestick patterns
  const significantPatterns = candlestickPatterns.filter(p =>
    p && p.significance && p.significance !== "weak" &&
    p.position >= data.length - 3);

  for (const pattern of significantPatterns) {
    if (pattern.bullish) {
      bullishScore += 5 * (pattern.significance === "strong" ? 1 : 0.5);
      categoryScores.candleAnalysis.bullish += 5 * (pattern.significance === "strong" ? 1 : 0.5);
    } else {
      bearishScore += 5 * (pattern.significance === "strong" ? 1 : 0.5);
      categoryScores.candleAnalysis.bearish += 5 * (pattern.significance === "strong" ? 1 : 0.5);
    }
  }

  // 2.3 Candle size relative to recent average (4%)
  // Check if recent candles are larger than average
  const recentCandleSizes = data.slice(-10).map(c => Math.abs(c.close - c.open));
  const avgCandleSize = recentCandleSizes.reduce((sum, size) => sum + size, 0) / recentCandleSizes.length;

  const latestCandle = data[data.length - 1];
  const latestCandleSize = Math.abs(latestCandle.close - latestCandle.open);

  if (latestCandleSize > avgCandleSize * 1.5) {
    // Large candle - check direction
    if (latestCandle.close > latestCandle.open) {
      bullishScore += 4;
      categoryScores.candleAnalysis.bullish += 4;
    } else {
      bearishScore += 4;
      categoryScores.candleAnalysis.bearish += 4;
    }
  }

  // 2.4 Multi-candle formations (4%)
  // Check for sequences in price action analysis
  if (priceActionAnalysis.sequences && priceActionAnalysis.sequences.length > 0) {
    // Filter for recent sequences
    const recentSequences = priceActionAnalysis.sequences.filter(s =>
      s.endIndex >= data.length - 5 && s.significance === "strong");

    for (const sequence of recentSequences) {
      if (sequence.type.includes("bullish")) {
        bullishScore += 4 * 0.5; // Split points among multiple sequences
        categoryScores.candleAnalysis.bullish += 4 * 0.5;
      } else if (sequence.type.includes("bearish")) {
        bearishScore += 4 * 0.5;
        categoryScores.candleAnalysis.bearish += 4 * 0.5;
      }
    }
  }

  // 3. TRENDLINES (20%)
  // 3.1 Major trendline breaks/tests (8%)
  // Check for trendline patterns in chart patterns
  const trendlinePatterns = chartPatterns.filter(p =>
    p.pattern.includes("trendline") ||
    p.pattern.includes("support_") ||
    p.pattern.includes("resistance_"));

  if (trendlinePatterns.length > 0) {
    // Count recent trendline breaks/tests
    const recentTrendlines = trendlinePatterns.filter(p => p.endIndex >= data.length - 10);

    for (const trendline of recentTrendlines) {
      if (trendline.bullish) {
        bullishScore += 8 / Math.max(1, recentTrendlines.length);
        categoryScores.trendlines.bullish += 8 / Math.max(1, recentTrendlines.length);
      } else {
        bearishScore += 8 / Math.max(1, recentTrendlines.length);
        categoryScores.trendlines.bearish += 8 / Math.max(1, recentTrendlines.length);
      }
    }
  }

  // 3.2 Multiple timeframe trendline confluence (5%)
  // Use multi-timeframe analysis for this
  if (multiTimeframeAnalysis.confirmation.bullish && multiTimeframeAnalysis.confirmation.strength === "strong") {
    bullishScore += 5;
    categoryScores.trendlines.bullish += 5;
  } else if (multiTimeframeAnalysis.confirmation.bearish && multiTimeframeAnalysis.confirmation.strength === "strong") {
    bearishScore += 5;
    categoryScores.trendlines.bearish += 5;
  }

  // 3.3 Trendline respect history (5%)
  // This is difficult to calculate directly, use chart patterns as proxy
  const strongTrendPatterns = chartPatterns.filter(p =>
    (p.pattern.includes("trendline") || p.pattern.includes("channel")) &&
    p.significance === "strong");

  if (strongTrendPatterns.length > 0) {
    // Use the most significant pattern
    const mostSignificant = strongTrendPatterns[0];

    if (mostSignificant.bullish) {
      bullishScore += 5;
      categoryScores.trendlines.bullish += 5;
    } else {
      bearishScore += 5;
      categoryScores.trendlines.bearish += 5;
    }
  }

  // 3.4 Angle of trendline (2%)
  // This is difficult to calculate directly, use market context as proxy
  if (marketContextAnalysis.trendStrength === "strong") {
    if (marketContextAnalysis.trendDirection === "up") {
      bullishScore += 2;
      categoryScores.trendlines.bullish += 2;
    } else if (marketContextAnalysis.trendDirection === "down") {
      bearishScore += 2;
      categoryScores.trendlines.bearish += 2;
    }
  }

  // 4. SUPPORT/RESISTANCE LEVELS (15%)
  // 4.1 Historical price levels with multiple tests (6%)
  const supportLevels = marketStructure.keyLevels.support;
  const resistanceLevels = marketStructure.keyLevels.resistance;

  // Filter support levels to only include those below current price
  const validSupportLevels = supportLevels.filter(level => level < currentPrice);

  // Filter resistance levels to only include those above current price
  const validResistanceLevels = resistanceLevels.filter(level => level > currentPrice);

  // Find nearest support (below current price)
  const nearestSupport = validSupportLevels.length > 0 ?
    validSupportLevels.reduce((nearest, level) =>
      (currentPrice - level) < (currentPrice - nearest) ? level : nearest,
      validSupportLevels[0]) : null;

  // Find nearest resistance (above current price)
  const nearestResistance = validResistanceLevels.length > 0 ?
    validResistanceLevels.reduce((nearest, level) =>
      (level - currentPrice) < (nearest - currentPrice) ? level : nearest,
      validResistanceLevels[0]) : null;

  // Check if price is near support or resistance
  if (nearestSupport && Math.abs(currentPrice - nearestSupport) / currentPrice < 0.02) {
    // Price is at support
    bullishScore += 6;
    categoryScores.supportResistance.bullish += 6;
  } else if (nearestResistance && Math.abs(currentPrice - nearestResistance) / currentPrice < 0.02) {
    // Price is at resistance
    bearishScore += 6;
    categoryScores.supportResistance.bearish += 6;
  }

  // 4.2 Volume profile high-volume nodes (6%)
  if (volumeProfileAnalysis.volumeProfile && volumeProfileAnalysis.volumeProfile.pointOfControl) {
    const poc = volumeProfileAnalysis.volumeProfile.pointOfControl;

    // Check if price is near point of control
    if (Math.abs(currentPrice - poc) / currentPrice < 0.02) {
      // At POC, check if coming from above or below
      if (data[data.length - 2].close > poc && currentPrice <= poc) {
        bearishScore += 6;
        categoryScores.supportResistance.bearish += 6;
      } else if (data[data.length - 2].close < poc && currentPrice >= poc) {
        bullishScore += 6;
        categoryScores.supportResistance.bullish += 6;
      }
    } else if (volumeProfileAnalysis.currentPriceLocation === "above_value_area") {
      bullishScore += 3;
      categoryScores.supportResistance.bullish += 3;
    } else if (volumeProfileAnalysis.currentPriceLocation === "below_value_area") {
      bearishScore += 3;
      categoryScores.supportResistance.bearish += 3;
    }
  }

  // 4.3 Psychological levels (round numbers) (3%)
  // Check if price is near a round number
  const priceStr = currentPrice.toString();
  const roundNumberMatch = priceStr.match(/^(\d+)(\.\d+)?$/);

  if (roundNumberMatch) {
    const wholePart = parseInt(roundNumberMatch[1]);

    // Check if it's a round number (ending in 00, 50, or 000)
    if (wholePart % 100 === 0 || wholePart % 50 === 0 || wholePart % 1000 === 0) {
      // It's a round number, check if we're approaching from below or above
      if (data[data.length - 2].close < currentPrice) {
        // Approaching from below - resistance
        bearishScore += 3;
        categoryScores.supportResistance.bearish += 3;
      } else {
        // Approaching from above - support
        bullishScore += 3;
        categoryScores.supportResistance.bullish += 3;
      }
    }
  }

  // 5. FIBONACCI ANALYSIS (10%)
  // 5.1 Key retracement level tests (0.5, 0.618, 0.786) (5%)
  if (fibonacciAnalysis.retracements) {
    // Extract key Fibonacci levels
    const keyLevels: number[] = [];

    // Check if we have the 0.5 level
    if (typeof fibonacciAnalysis.retracements["0.5"] === "number") {
      keyLevels.push(fibonacciAnalysis.retracements["0.5"] as number);
    }

    // Check if we have the 0.618 level
    if (typeof fibonacciAnalysis.retracements["0.618"] === "number") {
      keyLevels.push(fibonacciAnalysis.retracements["0.618"] as number);
    }

    // Check if we have the 0.786 level
    if (typeof fibonacciAnalysis.retracements["0.786"] === "number") {
      keyLevels.push(fibonacciAnalysis.retracements["0.786"] as number);
    }

    // Check if price is near any key Fibonacci level
    for (const level of keyLevels) {
      if (Math.abs(currentPrice - level) / currentPrice < 0.02) {
        // At a key level, check trend direction
        const trend = fibonacciAnalysis.retracements.trend || "unknown";

        if (trend === "uptrend") {
          bullishScore += 5 / keyLevels.length;
          categoryScores.fibonacci.bullish += 5 / keyLevels.length;
        } else if (trend === "downtrend") {
          bearishScore += 5 / keyLevels.length;
          categoryScores.fibonacci.bearish += 5 / keyLevels.length;
        }
      }
    }
  }

  // 5.2 Multiple Fibonacci confluence (3%)
  // Since we don't have proper clusters implementation, use a simpler approach
  if (fibonacciAnalysis.retracements) {
    // Count how many Fibonacci levels are within 2% of current price
    let nearbyLevels = 0;

    for (const [_, value] of Object.entries(fibonacciAnalysis.retracements)) {
      if (typeof value === "number" && Math.abs(currentPrice - value) / currentPrice < 0.02) {
        nearbyLevels++;
      }
    }

    // If we have multiple nearby levels, it's a confluence
    if (nearbyLevels >= 2) {
      // Check if we're approaching from above or below
      if (data[data.length - 2].close < currentPrice) {
        // Approaching from below - resistance
        bearishScore += 3;
        categoryScores.fibonacci.bearish += 3;
      } else {
        // Approaching from above - support
        bullishScore += 3;
        categoryScores.fibonacci.bullish += 3;
      }
    }
  }

  // 5.3 Historical respect of Fibonacci levels (2%)
  // Simplified approach - check if price has respected Fibonacci levels recently
  if (fibonacciAnalysis.retracements) {
    // Look for recent reversals near Fibonacci levels
    let fibRespect = false;
    const recentReversals = priceActionAnalysis.barByBar.twoBarReversals.filter(r =>
      r.position >= data.length - 10 && r.strength !== "weak");

    for (const reversal of recentReversals) {
      const reversalPrice = data[reversal.position].close;

      // Check if reversal happened near a Fibonacci level
      for (const [_, value] of Object.entries(fibonacciAnalysis.retracements)) {
        if (typeof value === "number" && Math.abs(reversalPrice - value) / reversalPrice < 0.02) {
          fibRespect = true;

          // Check direction of reversal
          if (reversal.direction === "up") {
            bullishScore += 2;
            categoryScores.fibonacci.bullish += 2;
          } else {
            bearishScore += 2;
            categoryScores.fibonacci.bearish += 2;
          }

          break;
        }
      }

      if (fibRespect) break;
    }
  }

  // 6. INDICATOR CONFIRMATION (5%)
  // 6.1 Momentum indicator alignment with price (2%)
  // Use RSI and MACD
  if (rsiCondition === "oversold" || macdCondition === "bullish" || macdCondition === "bullish crossover") {
    bullishScore += 2;
    categoryScores.indicators.bullish += 2;
  } else if (rsiCondition === "overbought" || macdCondition === "bearish" || macdCondition === "bearish crossover") {
    bearishScore += 2;
    categoryScores.indicators.bearish += 2;
  }

  // 6.2 Volume confirmation (2%)
  // Check if volume is increasing with price movement
  const recentVolumes = data.slice(-5).map(c => c.volume || 0);
  const avgVolume = recentVolumes.reduce((sum, vol) => sum + vol, 0) / recentVolumes.length;

  if (data[data.length - 1].volume > avgVolume * 1.5) {
    // Volume spike, check direction
    if (data[data.length - 1].close > data[data.length - 1].open) {
      bullishScore += 2;
      categoryScores.indicators.bullish += 2;
    } else {
      bearishScore += 2;
      categoryScores.indicators.bearish += 2;
    }
  }

  // 6.3 Volatility consideration (ATR) (1%)
  // Check if volatility is expanding or contracting
  if (bollingerSqueeze.isSqueezing) {
    // Volatility contraction - neutral
    // No points awarded
  } else if (bollingerSqueeze.potentialDirection === "up") {
    bullishScore += 1;
    categoryScores.indicators.bullish += 1;
  } else if (bollingerSqueeze.potentialDirection === "down") {
    bearishScore += 1;
    categoryScores.indicators.bearish += 1;
  }

  // DETERMINE FINAL SIGNAL
  // Calculate total scores as percentages
  const totalBullishPercentage = bullishScore / maxPossibleScore;
  const totalBearishPercentage = bearishScore / maxPossibleScore;

  // Add volatility filter
  // Import the volatility regime classification from market context
  // Calculate ATR for volatility analysis if not already done
  const volatilityAtr = calculateATR(data);
  const { volatilityRegime, volatilityScore } = classifyVolatilityRegime(data, volatilityAtr);

  // Flag if volatility is too low for reliable signals
  const volatilityTooLow = volatilityRegime === 'low' && volatilityScore < 30;

  // Log volatility information
  console.log(`AURA VOLATILITY FILTER: Regime: ${volatilityRegime}, Score: ${volatilityScore}, Too Low: ${volatilityTooLow}`);

  // Adjust scores based on volatility and market environment
  if (volatilityTooLow) {
    // Reduce confidence in signals when volatility is too low
    console.log(`AURA VOLATILITY FILTER: Reducing signal confidence due to low volatility`);
    bullishScore *= 0.7; // Reduced from 0.8
    bearishScore *= 0.7; // Reduced from 0.8
  } else if (volatilityRegime === 'high') {
    // Increase confidence in signals when volatility is high
    console.log(`AURA VOLATILITY FILTER: Increasing signal confidence due to high volatility`);
    bullishScore *= 1.2;
    bearishScore *= 1.2;
  }

  // Reduce weight of signals in range markets
  if (marketContextAnalysis.environment === 'range') {
    console.log(`AURA RANGE FILTER: Reducing signal confidence in range-bound market`);
    bullishScore *= 0.7;
    bearishScore *= 0.7;

    // Further reduce if range confidence is not high
    if (marketStructure.confidence !== 'high') {
      console.log(`AURA RANGE FILTER: Further reducing signal confidence due to low range confidence`);
      bullishScore *= 0.8;
      bearishScore *= 0.8;
    }
  }

  // For logging/debugging
  console.log(`AURA WEIGHTED SCORING:
    Market Structure: Bullish ${categoryScores.marketStructure.bullish.toFixed(1)}/${categoryScores.marketStructure.maxPossible}, Bearish ${categoryScores.marketStructure.bearish.toFixed(1)}/${categoryScores.marketStructure.maxPossible}
    Candle Analysis: Bullish ${categoryScores.candleAnalysis.bullish.toFixed(1)}/${categoryScores.candleAnalysis.maxPossible}, Bearish ${categoryScores.candleAnalysis.bearish.toFixed(1)}/${categoryScores.candleAnalysis.maxPossible}
    Trendlines: Bullish ${categoryScores.trendlines.bullish.toFixed(1)}/${categoryScores.trendlines.maxPossible}, Bearish ${categoryScores.trendlines.bearish.toFixed(1)}/${categoryScores.trendlines.maxPossible}
    Support/Resistance: Bullish ${categoryScores.supportResistance.bullish.toFixed(1)}/${categoryScores.supportResistance.maxPossible}, Bearish ${categoryScores.supportResistance.bearish.toFixed(1)}/${categoryScores.supportResistance.maxPossible}
    Fibonacci: Bullish ${categoryScores.fibonacci.bullish.toFixed(1)}/${categoryScores.fibonacci.maxPossible}, Bearish ${categoryScores.fibonacci.bearish.toFixed(1)}/${categoryScores.fibonacci.maxPossible}
    Indicators: Bullish ${categoryScores.indicators.bullish.toFixed(1)}/${categoryScores.indicators.maxPossible}, Bearish ${categoryScores.indicators.bearish.toFixed(1)}/${categoryScores.indicators.maxPossible}
    TOTAL: Bullish ${bullishScore.toFixed(1)}/${maxPossibleScore}, Bearish ${bearishScore.toFixed(1)}/${maxPossibleScore}
  `);

  // Check if we're in a range-bound market
  const isRangebound = marketContextAnalysis.environment === 'range';

  // Check if we're near support or resistance - more strict criteria
  const nearSupport = nearestSupport && (Math.abs(currentPrice - nearestSupport) / currentPrice) < 0.03; // Within 3% of support
  const nearResistance = nearestResistance && (Math.abs(currentPrice - nearestResistance) / currentPrice) < 0.03; // Within 3% of resistance

  // Log the calculations for debugging
  // Console logging removed

  // Calculate the absolute point difference between bullish and bearish scores
  const scoreDifferencePoints = Math.abs(bullishScore - bearishScore);
  console.log(`AURA SCORE DIFFERENCE: ${scoreDifferencePoints.toFixed(1)} points (${bullishScore > bearishScore ? 'bullish' : 'bearish'})`);

  // Calculate the ratio of bearish to bullish score
  const bearishToBullishRatio = bullishScore > 0 ? bearishScore / bullishScore : 999;
  console.log(`AURA SCORE RATIO: Bearish to Bullish ratio = ${bearishToBullishRatio.toFixed(2)}`);

  // Check if the bearish to bullish ratio is below 0.1 (very bullish)
  const isVeryBullish = bearishToBullishRatio < 0.1;
  console.log(`AURA VERY BULLISH CHECK: ${isVeryBullish ? 'YES' : 'NO'}`);

  // Calculate the ratio of bullish to bearish score
  const bullishToBearishRatio = bearishScore > 0 ? bullishScore / bearishScore : 999;
  console.log(`AURA SCORE RATIO: Bullish to Bearish ratio = ${bullishToBearishRatio.toFixed(2)}`);

  // Check if the bullish to bearish ratio is below 0.1 (very bearish)
  const isVeryBearish = bullishToBearishRatio < 0.1;
  console.log(`AURA VERY BEARISH CHECK: ${isVeryBearish ? 'YES' : 'NO'}`);

  // Determine if we have a bullish or bearish bias based on which score is higher
  const hasBullishBias = bullishScore > bearishScore;
  const hasBearishBias = bearishScore > bullishScore;

  // For extreme catalysts, we still use a higher bar
  const extremeBullishCatalyst = confidence === "HIGH" && signal === "LONG" && totalBullishPercentage >= 0.7 && (bullishScore - bearishScore) / maxPossibleScore >= 0.5;
  const extremeBearishCatalyst = confidence === "HIGH" && signal === "SHORT" && totalBearishPercentage >= 0.7 && (bearishScore - bullishScore) / maxPossibleScore >= 0.5;

  // For logging
  // Console logging removed

  // Determine which level we're closer to
  const distanceToSupport = nearestSupport ? Math.abs(currentPrice - nearestSupport) / currentPrice : 1;
  const distanceToResistance = nearestResistance ? Math.abs(currentPrice - nearestResistance) / currentPrice : 1;

  // We can only be near one level at a time - choose the closest one
  const isNearerToResistance = distanceToResistance < distanceToSupport;

  // Adjust our near flags to ensure we're only near one level
  const effectiveNearResistance = nearResistance && isNearerToResistance;
  const effectiveNearSupport = nearSupport && !isNearerToResistance;

  console.log(`AURA RANGE STRATEGY: Closer to ${isNearerToResistance ? 'resistance' : 'support'}, ` +
              `Effective near resistance: ${effectiveNearResistance}, Effective near support: ${effectiveNearSupport}`);

  // Always use score-based approach for signal generation
  // Set signal based on which score is higher, with significantly increased threshold
  const requiredDifference = 25; // Increased from 15 to require much stronger signals

  // Check if the symbol is a cryptocurrency
  const isCrypto = /^X:.*USD$/.test(symbol) ||
                  /^(BTC|ETH|XRP|LTC|DOGE|SOL|ADA|DOT|AVAX|MATIC|LINK|UNI|SHIB)$/.test(symbol.toUpperCase());

  if (bullishScore > bearishScore + requiredDifference) {
    signal = "LONG";

    // Set confidence based on the difference and absolute score
    // Further increased thresholds for confidence levels
    const scoreDifference = (bullishScore - bearishScore) / maxPossibleScore;

    if (scoreDifference >= 0.35 && totalBullishPercentage >= 0.55) { // Increased from 0.25/0.45
      confidence = "HIGH";
    } else if (scoreDifference >= 0.25 && totalBullishPercentage >= 0.45) { // Increased from 0.15/0.35
      confidence = "MEDIUM";
    } else {
      confidence = "LOW";
    }

    // Log range information if applicable
    if (isRangebound) {
      if (effectiveNearResistance) {
        console.log(`AURA RANGE INFO: Near resistance with bullish bias (bullish score ${bullishScore.toFixed(1)} > bearish score ${bearishScore.toFixed(1)}) - LONG signal`);
      } else if (effectiveNearSupport) {
        console.log(`AURA RANGE INFO: Near support with bullish bias (bullish score ${bullishScore.toFixed(1)} > bearish score ${bearishScore.toFixed(1)}) - LONG signal`);
      }
    }
  } else if (bearishScore > bullishScore + requiredDifference) {
    signal = "SHORT";

    // Set confidence based on the difference and absolute score
    // Further increased thresholds for confidence levels
    const scoreDifference = (bearishScore - bullishScore) / maxPossibleScore;

    if (scoreDifference >= 0.35 && totalBearishPercentage >= 0.55) { // Increased from 0.25/0.45
      confidence = "HIGH";
    } else if (scoreDifference >= 0.25 && totalBearishPercentage >= 0.45) { // Increased from 0.15/0.35
      confidence = "MEDIUM";
    } else {
      confidence = "LOW";
    }

    // Log range information if applicable
    if (isRangebound) {
      if (effectiveNearResistance) {
        console.log(`AURA RANGE INFO: Near resistance with bearish bias (bearish score ${bearishScore.toFixed(1)} > bullish score ${bullishScore.toFixed(1)}) - SHORT signal`);
      } else if (effectiveNearSupport) {
        console.log(`AURA RANGE INFO: Near support with bearish bias (bearish score ${bearishScore.toFixed(1)} > bullish score ${bullishScore.toFixed(1)}) - SHORT signal`);
      }
    }
  } else if (isCrypto) {
    // For cryptocurrencies, always provide a LONG or SHORT signal based on which score is higher
    if (bullishScore >= bearishScore) {
      signal = "LONG";
      confidence = "LOW";
      console.log(`AURA CRYPTO ANALYSIS: Forcing LONG signal for crypto ${symbol} (bullish score ${bullishScore.toFixed(1)} >= bearish score ${bearishScore.toFixed(1)})`);
    } else {
      signal = "SHORT";
      confidence = "LOW";
      console.log(`AURA CRYPTO ANALYSIS: Forcing SHORT signal for crypto ${symbol} (bearish score ${bearishScore.toFixed(1)} > bullish score ${bullishScore.toFixed(1)})`);
    }
  } else if (volatilityTooLow && Math.abs(bullishScore - bearishScore) < requiredDifference * 2) {
    // Very low volatility and scores are close - force NEUTRAL
    signal = "NEUTRAL";
    confidence = "LOW";
    console.log(`AURA VOLATILITY FILTER: Forcing NEUTRAL signal due to very low volatility (${volatilityScore}) and close scores`);
  } else {
    // Equal scores - neutral
    signal = "NEUTRAL";
    confidence = "LOW";

    if (isRangebound) {
      console.log(`AURA RANGE INFO: Equal scores (${bullishScore.toFixed(1)}) in range-bound market - NEUTRAL signal`);
    }
  }

  // Store the scores for output
  bullishCount = Math.round(bullishScore);
  bearishCount = Math.round(bearishScore);

  console.log(`AURA SIGNAL GENERATION: Bullish score: ${bullishScore.toFixed(1)}, Bearish score: ${bearishScore.toFixed(1)}, Final signal: ${signal}, Confidence: ${confidence}`);

  // Calculate risk management parameters using the new framework
  const sltp = calculateStopLossTakeProfit(
    data,
    signal as any,
    marketStructure.swingHighs,
    marketStructure.swingLows,
    marketStructure.keyLevels.support,
    marketStructure.keyLevels.resistance
  );

  // For backward compatibility, create a riskManagement object
  const riskManagement = {
    entryPrice: currentPrice,
    stopLoss: sltp.stopLoss,
    takeProfit: sltp.takeProfit,
    riskRewardRatio: sltp.riskRewardRatio,
    suggestedPositionSize: 0 // This will be calculated later if needed
  };

  // Create a simplified summary with only the most important findings and scores
  const summary = {
    recommendation: signal,
    confidence: confidence,
    bullishScore: bullishScore.toFixed(1),
    bearishScore: bearishScore.toFixed(1),
    // Add simplified findings with only the most important information
    findings: {
      // Market Structure Analysis - simplified
      marketStructure: {
        structure: marketStructure.structure,
        confidence: marketStructure.confidence,
        keyLevels: {
          support: marketStructure.keyLevels.support.slice(0, 3), // Only include top 3 support levels
          resistance: marketStructure.keyLevels.resistance.slice(0, 3) // Only include top 3 resistance levels
        }
      },
      // Candlestick Analysis - simplified
      candleAnalysis: {
        // Only include the most significant patterns (up to 3)
        significantPatterns: significantPatterns
          .filter(p => p.significance === 'strong')
          .slice(0, 3)
          .map(p => ({
            pattern: p.pattern,
            bullish: p.bullish
          }))
      },
      // Trendline Analysis - simplified
      trendlines: {
        // Only include dominant trend information
        multiTimeframe: {
          daily: multiTimeframeAnalysis.trends.daily,
          weekly: multiTimeframeAnalysis.trends.weekly,
          monthly: multiTimeframeAnalysis.trends.monthly,
          dominantTrend: multiTimeframeAnalysis.dominantTrend
        }
      },
      // Support/Resistance Analysis - using direct average of swing highs and lows
      supportResistance: {
        nearestSupport: nearestSupport,
        nearestResistance: nearestResistance,
        distanceToSupport: nearestSupport ? ((currentPrice - nearestSupport) / currentPrice * 100).toFixed(2) + '%' : 'N/A',
        distanceToResistance: nearestResistance ? ((nearestResistance - currentPrice) / currentPrice * 100).toFixed(2) + '%' : 'N/A',
        // Always ensure these are positive percentages for clarity
        distanceDown: nearestSupport ? Math.abs(((currentPrice - nearestSupport) / currentPrice * 100)).toFixed(2) + '%' : 'N/A',
        distanceUp: nearestResistance ? Math.abs(((nearestResistance - currentPrice) / currentPrice * 100)).toFixed(2) + '%' : 'N/A',
        // Include all key levels for reference
        keySupports: marketStructure.keyLevels.support,
        keyResistances: marketStructure.keyLevels.resistance,
        // Add a description of the levels
        supportDescription: nearestSupport ?
          `Support at ${nearestSupport.toFixed(2)} (${Math.abs(((currentPrice - nearestSupport) / currentPrice * 100)).toFixed(2)}% below current price)` :
          'No significant support levels found',
        resistanceDescription: nearestResistance ?
          `Resistance at ${nearestResistance.toFixed(2)} (${Math.abs(((nearestResistance - currentPrice) / currentPrice * 100)).toFixed(2)}% above current price)` :
          'No significant resistance levels found',
        // Add price range information
        priceRange: marketStructure.priceRange ? {
          support: marketStructure.priceRange.support,
          resistance: marketStructure.priceRange.resistance,
          rangeStart: marketStructure.priceRange.rangeStart,
          supportDescription: `Range support at ${marketStructure.priceRange.support.toFixed(2)} (${Math.abs(((currentPrice - marketStructure.priceRange.support) / currentPrice * 100)).toFixed(2)}% below current price)`,
          resistanceDescription: `Range resistance at ${marketStructure.priceRange.resistance.toFixed(2)} (${Math.abs(((marketStructure.priceRange.resistance - currentPrice) / currentPrice * 100)).toFixed(2)}% above current price)`,
          rangeWidth: ((marketStructure.priceRange.resistance - marketStructure.priceRange.support) / marketStructure.priceRange.support * 100).toFixed(2) + '%',
          positionInRange: ((currentPrice - marketStructure.priceRange.support) / (marketStructure.priceRange.resistance - marketStructure.priceRange.support) * 100).toFixed(2) + '%',
          method: 'Identified using swing high/low analysis with percent change calculations between swings'
        } : null,
        // Indicate the method used to find these levels
        method: 'Based on the mode (most frequently tested price levels) of swing highs and swing lows from the last 3 months, with outliers removed',
        // Indicate the strength of the levels based on distance from current price
        supportStrength: nearestSupport ?
          (Math.abs(currentPrice - nearestSupport) / currentPrice > 0.1 ? 'weak' :
           Math.abs(currentPrice - nearestSupport) / currentPrice > 0.05 ? 'moderate' : 'strong') : 'none',
        resistanceStrength: nearestResistance ?
          (Math.abs(currentPrice - nearestResistance) / currentPrice > 0.1 ? 'weak' :
           Math.abs(currentPrice - nearestResistance) / currentPrice > 0.05 ? 'moderate' : 'strong') : 'none',
        // Indicate if these are validated levels or fallbacks
        areValidatedLevels: marketStructure.keyLevels.support.length > 0 && marketStructure.keyLevels.resistance.length > 0,
        validationNote: 'Support is the mode (most frequently tested price level) of swing lows below current price and resistance is the mode of swing highs above current price from the last 3 months, with statistical outliers removed using the IQR method'
      },
      // Indicator Analysis - simplified
      indicators: {
        rsi: {
          value: rsi[lastIndex]?.toFixed(2),
          condition: rsiCondition
        },
        macd: {
          condition: macdCondition
        },
        bollingerBands: {
          squeeze: bollingerSqueeze.isSqueezing,
          condition: bbCondition
        }
      }
    },
    // Key price levels
    keyLevels: {
      currentPrice: currentPrice,
      support: nearestSupport || (currentPrice * 0.95), // Fallback to 5% below current price if no support found
      resistance: nearestResistance || (currentPrice * 1.05), // Fallback to 5% above current price if no resistance found
      stopLoss: sltp.stopLoss,
      takeProfit: sltp.takeProfit,
      stopLossExplanation: sltp.stopLossExplanation,
      takeProfitExplanation: sltp.takeProfitExplanation,
      riskRewardRatio: sltp.riskRewardRatio
    },
    // Overall market context - simplified summary
    marketContext: {
      trend: trend,
      environment: marketContextAnalysis.environment,
      volatility: {
        ...marketStructure.volatility,
        regime: volatilityRegime,
        score: volatilityScore,
        isTooLow: volatilityTooLow,
      },
      // Add range-bound trading strategy information
      rangeStrategy: isRangebound ? {
        nearSupport: effectiveNearSupport,
        nearResistance: effectiveNearResistance,
        closerTo: isNearerToResistance ? 'resistance' : 'support',
        distanceToSupport: (distanceToSupport * 100).toFixed(2) + '%',
        distanceToResistance: (distanceToResistance * 100).toFixed(2) + '%',
        scoreDifferencePoints: scoreDifferencePoints,
        bearishToBullishRatio: bearishToBullishRatio,
        bullishToBearishRatio: bullishToBearishRatio,
        isVeryBullish: isVeryBullish,
        isVeryBearish: isVeryBearish,
        hasBullishBias: hasBullishBias,
        hasBearishBias: hasBearishBias,
        extremeBullishCatalyst: extremeBullishCatalyst,
        extremeBearishCatalyst: extremeBearishCatalyst,
        strategyApplied: isRangebound,
        explanation: isRangebound ?
          (effectiveNearResistance && bullishScore > bearishScore ?
            `Price is within ${(distanceToResistance * 100).toFixed(2)}% of resistance with bullish bias (bullish score ${bullishScore.toFixed(1)} > bearish score ${bearishScore.toFixed(1)}) - LONG signal` :
           effectiveNearResistance && bearishScore > bullishScore ?
            `Price is within ${(distanceToResistance * 100).toFixed(2)}% of resistance with bearish bias (bearish score ${bearishScore.toFixed(1)} > bullish score ${bullishScore.toFixed(1)}) - SHORT signal` :
           effectiveNearResistance ?
            `Price is within ${(distanceToResistance * 100).toFixed(2)}% of resistance with equal scores - NEUTRAL signal` :
           effectiveNearSupport && bullishScore > bearishScore ?
            `Price is within ${(distanceToSupport * 100).toFixed(2)}% of support with bullish bias (bullish score ${bullishScore.toFixed(1)} > bearish score ${bearishScore.toFixed(1)}) - LONG signal` :
           effectiveNearSupport && bearishScore > bullishScore ?
            `Price is within ${(distanceToSupport * 100).toFixed(2)}% of support with bearish bias (bearish score ${bearishScore.toFixed(1)} > bullish score ${bullishScore.toFixed(1)}) - SHORT signal` :
           effectiveNearSupport ?
            `Price is within ${(distanceToSupport * 100).toFixed(2)}% of support with equal scores - NEUTRAL signal` :
            "Not near support/resistance - using score-based approach") :
          "Not in a range-bound market"
      } : null
    }
  };

  // Define the type for the simplified analysis
  interface SimplifiedAnalysis {
    symbol: string;
    signal: string;
    price: number;
    stopLoss: number;
    takeProfit: number;
    support: number;
    resistance: number;
    confidence: string;
    confidenceScore: number;
    bullishScore: number;
    bearishScore: number;
    marketEnvironment: string;
    trend: string;
  }

  // Define the type for the full analysis with simplified property
  interface FullAnalysis {
    symbol: string;
    currentPrice: number;
    trend: string;
    indicators: any;
    candlestickPatterns: any;
    candleAnatomy: any;
    priceAction: any;
    chartPatterns: any;
    marketStructure: any;
    marketContext: any;
    supportResistance: any;
    fibonacci: any;
    gapAnalysis: any;
    volumeProfile: any;
    signal: string;
    confidence: string;
    bullishCount: number;
    bearishCount: number;
    riskManagement: any;
    multiTimeframe: any;
    summary: any;
    simplified: SimplifiedAnalysis;
    optionsRecommendation?: OptionsRecommendation;
  }

  // Create the simplified analysis
  const simplifiedAnalysis: SimplifiedAnalysis = {
    symbol,
    signal,
    price: currentPrice,
    stopLoss: sltp.stopLoss,
    takeProfit: sltp.takeProfit,
    support: nearestSupport || (currentPrice * 0.95),
    resistance: nearestResistance || (currentPrice * 1.05),
    confidence,
    confidenceScore: bullishCount > bearishCount ?
      Math.round((bullishCount / (bullishCount + bearishCount)) * 100) :
      Math.round((bearishCount / (bullishCount + bearishCount)) * 100),
    bullishScore: bullishCount,
    bearishScore: bearishCount,
    marketEnvironment: marketContextAnalysis.environment,
    trend
  };

  // Create the full analysis result
  const fullAnalysis: FullAnalysis = {
    symbol,
    currentPrice,
    trend,
    indicators: {
      sma: {
        sma20: sma20[lastIndex],
        sma50: sma50[lastIndex],
        sma200: sma200[lastIndex]
      },
      ema: {
        ema12: ema12[lastIndex],
        ema26: ema26[lastIndex]
      },
      rsi: {
        value: rsi[lastIndex],
        condition: rsiCondition
      },
      macd: {
        macdLine: macd.macdLine[lastIndex],
        signalLine: macd.signalLine[lastIndex],
        histogram: macd.histogram[lastIndex],
        condition: macdCondition
      },
      bollingerBands: {
        upper: bollingerBands.bollingerBands.upper[lastIndex],
        middle: bollingerBands.bollingerBands.middle[lastIndex],
        lower: bollingerBands.bollingerBands.lower[lastIndex],
        condition: bbCondition,
        squeeze: {
          isSqueezing: bollingerSqueeze.isSqueezing,
          intensity: bollingerSqueeze.intensity,
          duration: bollingerSqueeze.duration,
          nearBreakout: bollingerSqueeze.nearBreakout,
          potentialDirection: bollingerSqueeze.potentialDirection
        }
      }
    },
    // Candlestick and price action analysis
    candlestickPatterns: candlePatterns,
    candleAnatomy: candleAnatomyData,
    priceAction: {
      barByBar: priceActionAnalysis.barByBar,
      momentum: priceActionAnalysis.momentum,
      sequences: priceActionAnalysis.sequences
    },
    // Chart patterns and market structure
    chartPatterns,
    marketStructure: {
      structure: marketStructure.structure,
      confidence: marketStructure.confidence,
      higherHighs: marketStructure.higherHighs,
      higherLows: marketStructure.higherLows,
      lowerHighs: marketStructure.lowerHighs,
      lowerLows: marketStructure.lowerLows,
      volatility: marketStructure.volatility
    },
    // Market context and environment
    marketContext: {
      environment: marketContextAnalysis.environment,
      environmentConfidence: marketContextAnalysis.environmentConfidence,
      volatilityRegime: marketContextAnalysis.volatilityRegime,
      volatilityScore: marketContextAnalysis.volatilityScore,
      rangeHigh: marketContextAnalysis.rangeHigh,
      rangeLow: marketContextAnalysis.rangeLow,
      trendDirection: marketContextAnalysis.trendDirection,
      trendStrength: marketContextAnalysis.trendStrength,
      breakoutDirection: marketContextAnalysis.breakoutDirection,
      breakoutStrength: marketContextAnalysis.breakoutStrength
    },
    // Support and resistance levels
    supportResistance: {
      support: marketStructure.keyLevels.support,
      resistance: marketStructure.keyLevels.resistance,
      // Add the price range information
      priceRange: marketStructure.priceRange ? {
        support: marketStructure.priceRange.support,
        resistance: marketStructure.priceRange.resistance,
        rangeStart: marketStructure.priceRange.rangeStart,
        rangeWidth: ((marketStructure.priceRange.resistance - marketStructure.priceRange.support) / marketStructure.priceRange.support * 100).toFixed(2) + '%',
        positionInRange: ((currentPrice - marketStructure.priceRange.support) / (marketStructure.priceRange.resistance - marketStructure.priceRange.support) * 100).toFixed(2) + '%'
      } : null
    },
    // Fibonacci analysis
    fibonacci: {
      retracements: fibonacciAnalysis.retracements,
      extensions: fibonacciAnalysis.extensions,
      clusters: fibonacciAnalysis.clusters
    },
    // Gap analysis
    gapAnalysis: {
      hasRecentGap: gapAnalysis.hasRecentGap,
      recentGaps: gapAnalysis.recentGaps,
      gapSummary: gapAnalysis.gapSummary
    },
    // Volume profile analysis
    volumeProfile: {
      valueArea: volumeProfileAnalysis.volumeProfile.valueArea,
      pointOfControl: volumeProfileAnalysis.volumeProfile.pointOfControl,
      currentPriceLocation: volumeProfileAnalysis.currentPriceLocation,
      distanceFromPOC: volumeProfileAnalysis.distanceFromPOC,
      distanceFromPOCPercent: volumeProfileAnalysis.distanceFromPOCPercent
    },
    // Final signal and confidence
    signal,
    confidence,
    bullishCount,
    bearishCount,
    riskManagement: {
      entryPrice: riskManagement.entryPrice,
      stopLoss: sltp.stopLoss,
      takeProfit: sltp.takeProfit,
      riskRewardRatio: sltp.riskRewardRatio,
      suggestedPositionSize: riskManagement.suggestedPositionSize,
      stopLossExplanation: sltp.stopLossExplanation,
      takeProfitExplanation: sltp.takeProfitExplanation,
      stopLossFactors: sltp.stopLossFactors,
      takeProfitFactors: sltp.takeProfitFactors
    },
    // Multi-timeframe analysis
    multiTimeframe: multiTimeframeAnalysis,
    // Summary section with key points
    summary,
    // Simplified analysis
    simplified: simplifiedAnalysis
  };

  // The simplified property is already set in the fullAnalysis object

  // If API key is provided, get options recommendation
  let optionsRec: OptionsRecommendation | undefined;
  if (apiKey) {
    try {
      // Get options recommendation
      optionsRec = await recommendOptionsContracts(symbol, currentPrice, signal, apiKey);

      // Add options recommendation to the full analysis
      fullAnalysis.optionsRecommendation = optionsRec;
    } catch (error) {
      console.error(`Error getting options recommendation for ${symbol}:`, error);
      // Don't fail the analysis if options recommendation fails
    }
  }

  // Return the full analysis
  return fullAnalysis;
}
