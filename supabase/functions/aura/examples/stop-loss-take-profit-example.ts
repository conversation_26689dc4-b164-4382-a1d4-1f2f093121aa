// Example of using the Aura Stop Loss & Take Profit Framework

import { Candle } from '../candlestick-patterns.ts';
import { analyzeMarketStructure } from '../market-structure.ts';
import { calculateStopLossTakeProfit } from '../risk-management/stop-loss-take-profit.ts';

// Sample function to demonstrate how to use the stop loss and take profit framework
export function demonstrateStopLossTakeProfit(candles: Candle[], signal: 'LONG' | 'SHORT'): void {
  // Step 1: Analyze market structure to get swing points and support/resistance levels
  const marketStructure = analyzeMarketStructure(candles);
  
  // Step 2: Calculate stop loss and take profit levels
  const sltp = calculateStopLossTakeProfit(
    candles,
    signal,
    marketStructure.swingHighs,
    marketStructure.swingLows,
    marketStructure.keyLevels.support,
    marketStructure.keyLevels.resistance
  );
  
  // Step 3: Display the results
  const currentPrice = candles[candles.length - 1].close;
  
  // Console logging removed
  
  // Console logging removed
  
  console.log('\nTake Profit Factors:');
  console.log(`1. Key Resistance/Support Targets (${(sltp.takeProfitFactors.keyResistanceSupportTargets.weight * 100).toFixed(0)}%): ${sltp.takeProfitFactors.keyResistanceSupportTargets.explanation}`);
  console.log(`2. Fibonacci Projection Targets (${(sltp.takeProfitFactors.fibonacciProjectionTargets.weight * 100).toFixed(0)}%): ${sltp.takeProfitFactors.fibonacciProjectionTargets.explanation}`);
  console.log(`3. Momentum-Based Targets (${(sltp.takeProfitFactors.momentumBasedTargets.weight * 100).toFixed(0)}%): ${sltp.takeProfitFactors.momentumBasedTargets.explanation}`);
}

// Example of how to integrate with technical analysis
export function integrateWithTechnicalAnalysis(
  candles: Candle[],
  bullishScore: number,
  bearishScore: number
): { signal: 'LONG' | 'SHORT' | 'NEUTRAL', stopLoss: number, takeProfit: number, explanation: string } {
  // Determine signal based on scores
  let signal: 'LONG' | 'SHORT' | 'NEUTRAL' = 'NEUTRAL';
  
  if (bullishScore > bearishScore) {
    signal = 'LONG';
  } else if (bearishScore > bullishScore) {
    signal = 'SHORT';
  }
  
  // If we have a directional signal, calculate stop loss and take profit
  if (signal !== 'NEUTRAL') {
    // Analyze market structure
    const marketStructure = analyzeMarketStructure(candles);
    
    // Calculate stop loss and take profit
    const sltp = calculateStopLossTakeProfit(
      candles,
      signal,
      marketStructure.swingHighs,
      marketStructure.swingLows,
      marketStructure.keyLevels.support,
      marketStructure.keyLevels.resistance
    );
    
    return {
      signal,
      stopLoss: sltp.stopLoss,
      takeProfit: sltp.takeProfit,
      explanation: `${signal} signal with stop loss at ${sltp.stopLoss.toFixed(2)} and take profit at ${sltp.takeProfit.toFixed(2)}. Risk-reward ratio: 1:${sltp.riskRewardRatio.toFixed(2)}.`
    };
  }
  
  // Default return for neutral signal
  return {
    signal: 'NEUTRAL',
    stopLoss: 0,
    takeProfit: 0,
    explanation: 'No directional bias detected.'
  };
}
