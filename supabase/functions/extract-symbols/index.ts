import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

// Handle OPTIONS requests for CORS
function handleOptions() {
  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
}

// Extract symbols from user message using Gemini
async function extractSymbols(text: string): Promise<string[]> {
  if (!text) return [];

  try {
    const apiKey = Deno.env.get('GEMINI_API_KEY');
    if (!apiKey) return [];

    const prompt = `Extract stock symbols from this text. Return only valid stock symbols as a JSON array.

Text: "${text}"

Rules:
1. Return ONLY valid stock symbols
2. Maximum 5 symbols
3. No ETFs, futures, or derivatives
4. Format: ["AAPL", "MSFT"]
5. If no symbols found, return []`;

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        contents: [{ parts: [{ text: prompt }] }],
        generationConfig: { temperature: 0.1, maxOutputTokens: 100 }
      })
    });

    if (!response.ok) return [];

    const data = await response.json();
    const extractedText = data.candidates?.[0]?.content?.parts?.[0]?.text?.trim() || '';

    const jsonMatch = extractedText.match(/\[.*\]/s);
    if (!jsonMatch) return [];

    const symbols = JSON.parse(jsonMatch[0]);
    return Array.isArray(symbols) ? symbols.filter(s => typeof s === 'string') : [];
  } catch (error) {
    console.error('Symbol extraction error:', error);
    return [];
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return handleOptions();
  }

  try {
    const { message } = await req.json();

    if (!message) {
      return new Response(JSON.stringify({ 
        error: 'Message is required' 
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const symbols = await extractSymbols(message);

    return new Response(JSON.stringify({ 
      symbols 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    // Console logging removed
    return new Response(JSON.stringify({ 
      error: 'Failed to extract symbols',
      details: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});
