import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// Initialize Supabase client with admin privileges
const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Helper function for logging
};

// Helper function for logging info
};

// Main serve function
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  try {
    // Parse the request body
    let body;
    try {
      const text = await req.text();
      body = JSON.parse(text);
    } catch (e) {
      return new Response(JSON.stringify({ error: 'Invalid request body', details: e.message }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Get the authenticated user's ID
    const authHeader = req.headers.get('Authorization');

    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'No authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Get the authenticated user
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));

      if (userError || !user) {
        return new Response(JSON.stringify({ error: 'Unauthorized', details: userError?.message }), {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }


      // Extract the endpoint from the request
      const { endpoint, ...data } = body;

      // Validate the endpoint
      if (!endpoint) {
        return new Response(JSON.stringify({ error: 'Endpoint is required' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }


      // Make the request to api.Osis.co
      const apiUrl = `https://api.Osis.co:443/api/v1/${endpoint}`;

      try {

        // Create a timeout promise
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout after 30 seconds')), 30000);
        });

        // Create the fetch promise
        const fetchPromise = fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify(data)
        });

        // Race the fetch against the timeout
        const response = await Promise.race([fetchPromise, timeoutPromise]) as Response;


        // Check if the response is OK
        if (!response.ok) {
          const errorText = await response.text();

          // Return a 200 OK with error details instead of forwarding the error status
          // This prevents the client from seeing a 500 error
          return new Response(JSON.stringify({
            success: false,
            error: `API request failed with status ${response.status}`,
            details: errorText
          }), {
            status: 200, // Return 200 OK even for errors
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }

        // Parse the response
        const responseText = await response.text();

        try {
          // Handle empty responses
          if (!responseText || responseText.trim() === '') {
            return new Response(JSON.stringify({
              success: true,
              data: {}
            }), {
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            });
          }

          // Clean the response text - replace NaN with null
          const cleanedText = responseText.replace(/: *NaN/g, ': null');
          const responseData = JSON.parse(cleanedText);

          // Return the response
          return new Response(JSON.stringify({
            success: true,
            data: responseData
          }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        } catch (parseError) {

          // Return a 200 OK with error details
          return new Response(JSON.stringify({
            success: false,
            error: 'Failed to parse API response',
            details: parseError.message,
            responsePreview: responseText.substring(0, 100) + '...'
          }), {
            status: 200, // Return 200 OK even for errors
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }
      } catch (fetchError) {
        return new Response(JSON.stringify({
          error: 'Error connecting to api.Osis.co',
          details: fetchError.message
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }
    } catch (authError) {
      return new Response(JSON.stringify({
        error: 'Authentication error',
        details: authError.message
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }
  } catch (error) {
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error.message,
      type: error.constructor.name
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
