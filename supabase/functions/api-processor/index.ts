import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// Get the encryption key from environment variables
const ENCRYPTION_KEY = Deno.env.get('ENCRYPTION_KEY') || '';

// Initialize Supabase client with admin privileges
const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Helper function for logging
};

// Helper function for logging info
};

/**
 * Decrypts data using AES-GCM
 * @param encryptedData - Base64 encoded encrypted data with IV
 * @param password - The password to use for decryption
 * @returns The decrypted data
 */
async function decryptData(encryptedData: string, password: string): Promise<string> {
  try {
    // Convert from base64
    const encryptedBytes = Uint8Array.from(atob(encryptedData), c => c.charCodeAt(0));

    // Extract IV (first 12 bytes)
    const iv = encryptedBytes.slice(0, 12);

    // Extract encrypted data (everything after IV)
    const data = encryptedBytes.slice(12);

    // Derive the key
    const encoder = new TextEncoder();
    const passwordBuffer = encoder.encode(password);

    // Import the password as a key
    const baseKey = await crypto.subtle.importKey(
      'raw',
      passwordBuffer,
      { name: 'PBKDF2' },
      false,
      ['deriveKey']
    );

    // Salt for key derivation
    const salt = new Uint8Array([
      0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x53, 0x61,
      0x6c, 0x74, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36
    ]);

    // Derive a key using PBKDF2
    const key = await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt,
        iterations: 100000,
        hash: 'SHA-256'
      },
      baseKey,
      { name: 'AES-GCM', length: 256 },
      false,
      ['decrypt']
    );

    // Decrypt the data
    const decryptedBuffer = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv
      },
      key,
      data
    );

    // Decode the data
    const decoder = new TextDecoder();
    return decoder.decode(decryptedBuffer);
  } catch (error) {
    throw new Error('Failed to decrypt data');
  }
}

/**
 * Deobfuscates the data by extracting it from the generic object
 * @param data - The obfuscated data
 * @returns The original data
 */
function deobfuscateData(data: any): any {
  return data.client_data;
}

// Main serve function
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  try {
    // Parse the request body
    let body;
    try {
      const text = await req.text();
      body = JSON.parse(text);
    } catch (e) {
      return new Response(JSON.stringify({ error: 'Invalid request body', details: e.message }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Get the authenticated user's ID
    const authHeader = req.headers.get('Authorization');

    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'No authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Get the authenticated user
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));

      if (userError || !user) {
        return new Response(JSON.stringify({ error: 'Unauthorized', details: userError?.message }), {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }


      // Extract and decrypt the data
      const { data: encryptedData, metadata } = body;

      if (!encryptedData) {
        return new Response(JSON.stringify({ error: 'Missing data parameter' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      // Decrypt the request data
      const decryptedData = await decryptData(encryptedData, ENCRYPTION_KEY);

      // Parse the decrypted data
      const parsedData = JSON.parse(decryptedData);

      // Extract endpoint and data
      const { endpoint, ...requestData } = parsedData;

      // Validate the endpoint
      if (!endpoint) {
        return new Response(JSON.stringify({ error: 'Endpoint is required' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }


      // Make the request to api.Osis.co
      const apiUrl = `https://api.Osis.co:443/api/v1/${endpoint}`;

      try {

        // Create a timeout promise
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout after 30 seconds')), 30000);
        });

        // Create the fetch promise
        const fetchPromise = fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify(requestData)
        });

        // Race the fetch against the timeout
        const response = await Promise.race([fetchPromise, timeoutPromise]) as Response;


        // Check if the response is OK
        if (!response.ok) {
          const errorText = await response.text();

          // Return a 200 OK with error details instead of forwarding the error status
          return new Response(JSON.stringify({
            success: false,
            error: `API request failed with status ${response.status}`,
            details: errorText
          }), {
            status: 200, // Return 200 OK even for errors
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }

        // Parse the response
        const responseText = await response.text();

        try {
          // Handle empty responses
          if (!responseText || responseText.trim() === '') {
            return new Response(JSON.stringify({
              success: true,
              data: {}
            }), {
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            });
          }

          // Clean the response text - replace NaN with null
          const cleanedText = responseText.replace(/: *NaN/g, ': null');
          const responseData = JSON.parse(cleanedText);

          // Return the response
          return new Response(JSON.stringify({
            success: true,
            data: responseData
          }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        } catch (parseError) {

          // Return a 200 OK with error details
          return new Response(JSON.stringify({
            success: false,
            error: 'Failed to parse API response',
            details: parseError.message,
            responsePreview: responseText.substring(0, 100) + '...'
          }), {
            status: 200, // Return 200 OK even for errors
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }
      } catch (fetchError) {
        return new Response(JSON.stringify({
          error: 'Error connecting to API',
          details: fetchError.message
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }
    } catch (authError) {
      return new Response(JSON.stringify({
        error: 'Authentication error',
        details: authError.message
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }
  } catch (error) {
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error.message,
      type: error.constructor.name
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
