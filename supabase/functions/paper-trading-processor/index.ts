import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Polygon API key for real-time price data
const polygonApiKey = Deno.env.get('POLYGON_API_KEY')!

interface PendingOrder {
  id: string
  account_id: string
  user_id: string
  symbol: string
  order_type: string
  side: string
  quantity: number
  price?: number
  stop_price?: number
  time_in_force: string
  expires_at?: string
}

// Get current market price from Polygon API
async function getCurrentPrice(symbol: string): Promise<number> {
  try {
    // Try to get the latest trade first
    const url = `https://api.polygon.io/v2/last/trade/${symbol}?apiKey=${polygonApiKey}`
    const response = await fetch(url)
    const data = await response.json()
    
    if (data.results && data.results.price) {
      return data.results.price
    }
    
    // Fallback to previous close if last trade not available
    const closeUrl = `https://api.polygon.io/v2/aggs/ticker/${symbol}/prev?apiKey=${polygonApiKey}`
    const closeResponse = await fetch(closeUrl)
    const closeData = await closeResponse.json()
    
    if (closeData.results && closeData.results.length > 0) {
      return closeData.results[0].c
    }
    
    throw new Error(`No price data available for ${symbol}`)
  } catch (error) {
    console.error(`Error fetching price for ${symbol}:`, error)
    throw error
  }
}

// Process pending limit orders
async function processLimitOrders() {
  console.log('Processing limit orders...')
  
  const { data: pendingOrders, error } = await supabase
    .from('paper_trading_orders')
    .select('*')
    .eq('status', 'pending')
    .eq('order_type', 'limit')
  
  if (error) {
    console.error('Error fetching pending limit orders:', error)
    return
  }
  
  if (!pendingOrders || pendingOrders.length === 0) {
    console.log('No pending limit orders to process')
    return
  }
  
  console.log(`Processing ${pendingOrders.length} pending limit orders`)
  
  for (const order of pendingOrders) {
    try {
      const currentPrice = await getCurrentPrice(order.symbol)
      let shouldFill = false
      
      // Check if limit order should be filled
      if (order.side === 'buy' && currentPrice <= order.price) {
        shouldFill = true
      } else if (order.side === 'sell' && currentPrice >= order.price) {
        shouldFill = true
      }
      
      if (shouldFill) {
        await fillOrder(order, currentPrice)
        console.log(`Filled limit order ${order.id} for ${order.symbol} at $${currentPrice}`)
      }
    } catch (error) {
      console.error(`Error processing limit order ${order.id}:`, error)
    }
  }
}

// Process pending stop orders
async function processStopOrders() {
  console.log('Processing stop orders...')
  
  const { data: pendingOrders, error } = await supabase
    .from('paper_trading_orders')
    .select('*')
    .eq('status', 'pending')
    .eq('order_type', 'stop')
  
  if (error) {
    console.error('Error fetching pending stop orders:', error)
    return
  }
  
  if (!pendingOrders || pendingOrders.length === 0) {
    console.log('No pending stop orders to process')
    return
  }
  
  console.log(`Processing ${pendingOrders.length} pending stop orders`)
  
  for (const order of pendingOrders) {
    try {
      const currentPrice = await getCurrentPrice(order.symbol)
      let shouldTrigger = false
      
      // Check if stop order should be triggered
      if (order.side === 'buy' && currentPrice >= order.stop_price) {
        shouldTrigger = true
      } else if (order.side === 'sell' && currentPrice <= order.stop_price) {
        shouldTrigger = true
      }
      
      if (shouldTrigger) {
        await fillOrder(order, currentPrice)
        console.log(`Triggered stop order ${order.id} for ${order.symbol} at $${currentPrice}`)
      }
    } catch (error) {
      console.error(`Error processing stop order ${order.id}:`, error)
    }
  }
}

// Fill an order
async function fillOrder(order: PendingOrder, fillPrice: number) {
  const orderValue = order.quantity * fillPrice
  
  // Update order status
  const { error: orderError } = await supabase
    .from('paper_trading_orders')
    .update({
      status: 'filled',
      filled_quantity: order.quantity,
      avg_fill_price: fillPrice,
      total_value: orderValue,
      filled_at: new Date().toISOString()
    })
    .eq('id', order.id)
  
  if (orderError) {
    throw new Error(`Failed to update order: ${orderError.message}`)
  }
  
  // Update account balance
  const balanceChange = order.side === 'buy' ? -orderValue : orderValue
  const { error: balanceError } = await supabase.rpc('update_account_balance', {
    p_account_id: order.account_id,
    p_balance_change: balanceChange,
    p_is_realized: order.side === 'sell'
  })
  
  if (balanceError) {
    console.error('Error updating account balance:', balanceError)
  }
  
  // Update or create position
  await updatePosition(order.account_id, order.user_id, order.symbol, order.side, order.quantity, fillPrice)
  
  // Create transaction record
  const { error: transactionError } = await supabase
    .from('paper_trading_transactions')
    .insert({
      account_id: order.account_id,
      user_id: order.user_id,
      order_id: order.id,
      symbol: order.symbol,
      transaction_type: order.side,
      quantity: order.quantity,
      price: fillPrice,
      amount: order.side === 'buy' ? -orderValue : orderValue,
      commission: 0,
      description: `${order.side.toUpperCase()} ${order.quantity} shares of ${order.symbol} at $${fillPrice}`
    })
  
  if (transactionError) {
    console.error('Error creating transaction record:', transactionError)
  }
}

// Update position after order execution
async function updatePosition(accountId: string, userId: string, symbol: string, side: string, quantity: number, price: number) {
  const { data: existingPosition } = await supabase
    .from('paper_trading_positions')
    .select('*')
    .eq('account_id', accountId)
    .eq('symbol', symbol)
    .single()
  
  if (!existingPosition) {
    // Create new position
    if (side === 'buy') {
      await supabase
        .from('paper_trading_positions')
        .insert({
          account_id: accountId,
          user_id: userId,
          symbol: symbol,
          quantity: quantity,
          avg_cost: price,
          market_price: price,
          market_value: quantity * price,
          total_cost: quantity * price,
          unrealized_pnl: 0,
          unrealized_pnl_percent: 0,
          last_price_update: new Date().toISOString()
        })
    }
  } else {
    // Update existing position
    const newQuantity = side === 'buy' ? 
      existingPosition.quantity + quantity : 
      existingPosition.quantity - quantity
    
    if (newQuantity === 0) {
      // Close position
      await supabase
        .from('paper_trading_positions')
        .delete()
        .eq('account_id', accountId)
        .eq('symbol', symbol)
    } else {
      // Update position
      const newAvgCost = side === 'buy' ?
        ((existingPosition.avg_cost * existingPosition.quantity) + (price * quantity)) / newQuantity :
        existingPosition.avg_cost
      
      await supabase
        .from('paper_trading_positions')
        .update({
          quantity: newQuantity,
          avg_cost: newAvgCost,
          market_price: price,
          market_value: newQuantity * price,
          total_cost: Math.abs(newQuantity) * newAvgCost,
          unrealized_pnl: (price - newAvgCost) * newQuantity,
          unrealized_pnl_percent: ((price - newAvgCost) / newAvgCost) * 100,
          last_price_update: new Date().toISOString()
        })
        .eq('account_id', accountId)
        .eq('symbol', symbol)
    }
  }
}

// Expire DAY orders after market close
async function expireDayOrders() {
  // Console logging removed
  
  const now = new Date()
  const { data: expiredOrders, error } = await supabase
    .from('paper_trading_orders')
    .select('id')
    .eq('status', 'pending')
    .eq('time_in_force', 'DAY')
    .lt('expires_at', now.toISOString())
  
  if (error) {
    // Console logging removed
    return
  }
  
  if (expiredOrders && expiredOrders.length > 0) {
    const { error: updateError } = await supabase
      .from('paper_trading_orders')
      .update({ status: 'expired' })
      .in('id', expiredOrders.map(o => o.id))
    
    if (updateError) {
      // Console logging removed
    } else {
      // Console logging removed
    }
  }
}

// Update market prices for all positions
async function updateAllPositionPrices() {
  console.log('Updating market prices for all positions...')
  
  // Get all unique symbols from positions
  const { data: symbols, error } = await supabase
    .from('paper_trading_positions')
    .select('symbol')
    .neq('quantity', 0)
  
  if (error) {
    console.error('Error fetching position symbols:', error)
    return
  }
  
  if (!symbols || symbols.length === 0) {
    console.log('No positions to update')
    return
  }
  
  const uniqueSymbols = [...new Set(symbols.map(s => s.symbol))]
  console.log(`Updating prices for ${uniqueSymbols.length} symbols`)
  
  for (const symbol of uniqueSymbols) {
    try {
      const currentPrice = await getCurrentPrice(symbol)
      
      // Update all positions for this symbol
      const { error: updateError } = await supabase.rpc('update_position_market_values', {
        p_symbol: symbol,
        p_market_price: currentPrice
      })
      
      if (updateError) {
        console.error(`Error updating prices for ${symbol}:`, updateError)
      }
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100))
    } catch (error) {
      console.error(`Failed to get price for ${symbol}:`, error)
    }
  }
  
  console.log('Finished updating market prices')
}

// Main processing function
async function processOrders() {
  try {
    console.log('Starting order processing cycle...')
    
    // Process different types of orders
    await processLimitOrders()
    await processStopOrders()
    await expireDayOrders()
    await updateAllPositionPrices()
    
    console.log('Order processing cycle completed')
    
    return { success: true, message: 'Order processing completed successfully' }
  } catch (error) {
    console.error('Error in order processing:', error)
    return { success: false, error: error.message }
  }
}

// HTTP handler
serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    })
  }

  try {
    const result = await processOrders()
    
    return new Response(
      JSON.stringify(result),
      { 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        status: result.success ? 200 : 500
      }
    )
  } catch (error) {
    console.error('Handler error:', error)
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      { 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        status: 500 
      }
    )
  }
})
