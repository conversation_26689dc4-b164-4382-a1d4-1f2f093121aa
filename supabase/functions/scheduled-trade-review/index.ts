// Scheduled Trade Review Edge Function
// This function is triggered by a cron job to run the daily review process

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { DailyReview } from "../aura/trade-review/daily-review.ts";

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// CRON secret for security
const CRON_SECRET = Deno.env.get('CRON_SECRET') || '';

serve(async (req) => {
  try {
    // Verify this is a legitimate cron job request
    const authHeader = req.headers.get('Authorization');
    
    if (!authHeader || authHeader !== `Bearer ${CRON_SECRET}`) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    console.log('Starting scheduled trade review...');
    
    // Run the daily review
    const dailyReview = new DailyReview(supabaseUrl, supabaseServiceKey);
    const result = await dailyReview.runDailyReview();
    
    if (!result.success) {
      throw new Error(result.error);
    }
    
    // Log the results
    // Console logging removed
    
    // Save a record of the review in the database
    await supabase
      .from('system_logs')
      .insert({
        event: 'daily_review',
        details: {
          reviewed_trades: result.data.reviewed_trades,
          insights_count: result.data.insights.length,
          daily_win_rate: result.data.daily_performance?.win_rate,
          weekly_win_rate: result.data.weekly_performance?.win_rate,
          monthly_win_rate: result.data.monthly_performance?.win_rate
        },
        timestamp: new Date().toISOString()
      });
    
    return new Response(JSON.stringify({
      success: true,
      message: 'Daily review completed successfully',
      data: {
        reviewed_trades: result.data.reviewed_trades,
        insights_count: result.data.insights.length
      }
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    // Console logging removed
    
    // Log the error
    await supabase
      .from('system_logs')
      .insert({
        event: 'daily_review_error',
        details: {
          error: error.message,
          stack: error.stack
        },
        timestamp: new Date().toISOString()
      });
    
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
});
