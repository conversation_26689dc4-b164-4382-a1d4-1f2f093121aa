import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { WhopServerSdk } from 'npm:@whop/api@0.0.29';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// Initialize Supabase client
const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
);

// Initialize Whop SDK
const whopSdk = WhopServerSdk({
  appId: Deno.env.get('WHOP_APP_ID') || '',
  appApiKey: Deno.env.get('WHOP_API_KEY') || '',
  onBehalfOfUserId: Deno.env.get('WHOP_AGENT_USER_ID') || '',
  companyId: Deno.env.get('WHOP_COMPANY_ID') || ''
});

// Log environment variables for debugging (without exposing sensitive data)
// Console logging removed

interface DistributeAgentRequest {
  agentId: string;
  whopCompanyId: string;
  whopExperienceId?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get user from authorization header
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      throw new Error('Invalid authentication token');
    }

    console.log('🔐 Authenticated user:', user.id);

    // Parse request body
    const requestData: DistributeAgentRequest = await req.json();
    
    if (!requestData.agentId || !requestData.whopCompanyId) {
      throw new Error('Missing required fields: agentId and whopCompanyId');
    }

    console.log('📦 Distributing agent:', {
      agentId: requestData.agentId,
      companyId: requestData.whopCompanyId,
      experienceId: requestData.whopExperienceId
    });

    // Debug: Log user information
    console.log('🔍 User information:', {
      userId: user.id,
      email: user.email,
      userMetadata: user.user_metadata,
      whopUserId: user.user_metadata?.whop_user_id
    });

    // Verify user owns the agent
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('id, name, user_id')
      .eq('id', requestData.agentId)
      .eq('user_id', user.id)
      .single();

    if (agentError || !agent) {
      console.error('❌ Agent verification failed:', {
        agentId: requestData.agentId,
        userId: user.id,
        error: agentError
      });
      throw new Error('Agent not found or access denied');
    }

    console.log('✅ Agent ownership verified:', {
      agentId: agent.id,
      agentName: agent.name,
      ownerId: agent.user_id
    });

    // Check if user has admin access to the Whop company
    const userWhopId = user.user_metadata?.whop_user_id;
    if (!userWhopId) {
      console.error('❌ User is not a Whop user:', {
        userId: user.id,
        userMetadata: user.user_metadata
      });
      throw new Error('User is not a Whop user');
    }

    console.log('🔍 Checking Whop company access:', {
      userWhopId,
      companyId: requestData.whopCompanyId
    });

    // Check user's access level to the company
    let accessResult;
    try {
      accessResult = await whopSdk.access.checkIfUserHasAccessToCompany({
        companyId: requestData.whopCompanyId,
        userId: userWhopId,
      });

      console.log('📋 Whop access check result:', {
        hasAccess: accessResult.hasAccess,
        accessLevel: accessResult.accessLevel,
        userWhopId,
        companyId: requestData.whopCompanyId
      });
    } catch (whopError) {
      console.error('❌ Whop SDK access check failed:', {
        error: whopError.message,
        userWhopId,
        companyId: requestData.whopCompanyId
      });
      throw new Error(`Failed to verify Whop access: ${whopError.message}`);
    }

    if (!accessResult.hasAccess) {
      console.error('❌ User does not have access to Whop company:', {
        userWhopId,
        companyId: requestData.whopCompanyId,
        accessResult
      });
      throw new Error('User does not have access to this Whop company');
    }

    if (accessResult.accessLevel !== 'admin') {
      console.error('❌ User is not an admin:', {
        userWhopId,
        companyId: requestData.whopCompanyId,
        accessLevel: accessResult.accessLevel,
        required: 'admin'
      });
      throw new Error(`Only Whop company owners/admins can distribute agents. Current access level: ${accessResult.accessLevel}`);
    }

    console.log('✅ Whop admin access verified:', {
      userWhopId,
      companyId: requestData.whopCompanyId,
      accessLevel: accessResult.accessLevel
    });

    // Get all company members using the V5 API
    console.log('🔍 Fetching company members for company:', requestData.whopCompanyId);

    const whopApiKey = Deno.env.get('WHOP_API_KEY');
    const apiUrl = `https://api.whop.com/api/v5/app/members?company_id=${requestData.whopCompanyId}&per=50`;

    console.log('🔧 API call details:', {
      url: apiUrl,
      hasApiKey: !!whopApiKey,
      apiKeyPrefix: whopApiKey?.substring(0, 10) + '...',
      companyId: requestData.whopCompanyId
    });

    const membersResponse = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${whopApiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (!membersResponse.ok) {
      const errorText = await membersResponse.text();
      console.error('❌ Failed to fetch company members:', {
        status: membersResponse.status,
        statusText: membersResponse.statusText,
        error: errorText,
        companyId: requestData.whopCompanyId
      });
      throw new Error(`Failed to fetch company members: ${membersResponse.statusText} (${membersResponse.status})`);
    }

    const membersData = await membersResponse.json();
    console.log('📋 Found company members:', {
      totalMembers: membersData.data?.length || 0,
      pagination: membersData.pagination,
      companyId: requestData.whopCompanyId
    });

    // Prepare member data for distribution - V5 API has nested user object
    const memberData = membersData.data?.map((member: any) => ({
      id: member.user_id,
      username: member.user?.username,
      email: member.user?.email,
      name: member.user?.name,
      member_id: member.id,
      status: member.status
    })) || [];

    console.log('📤 Calling distribution function with data:', {
      agentId: requestData.agentId,
      distributorId: user.id,
      whopCompanyId: requestData.whopCompanyId,
      memberCount: memberData.length,
      experienceId: requestData.whopExperienceId
    });

    // Call the distribution function
    const { data: distributionResult, error: distributionError } = await supabase
      .rpc('distribute_agent_to_whop_members', {
        p_agent_id: requestData.agentId,
        p_distributor_id: user.id,
        p_whop_company_id: requestData.whopCompanyId,
        p_member_data: memberData,
        p_whop_experience_id: requestData.whopExperienceId
      });

    if (distributionError) {
      throw new Error(`Distribution failed: ${distributionError.message}`);
    }

    const result = distributionResult[0];
    
    if (!result.success) {
      throw new Error(`Distribution failed: ${result.errors?.join(', ')}`);
    }

    console.log('✅ Agent distributed successfully:', {
      distributionId: result.distribution_id,
      membersAdded: result.members_added,
      errors: result.errors
    });

    return new Response(
      JSON.stringify({
        success: true,
        message: `Agent "${agent.name}" has been distributed to ${result.members_added} Whop members`,
        distributionId: result.distribution_id,
        membersAdded: result.members_added,
        errors: result.errors?.length > 0 ? result.errors : undefined
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('❌ Error in whop-distribute-agent function:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'An unexpected error occurred'
      }),
      {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
