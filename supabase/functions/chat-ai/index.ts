// @deno-types="https://deno.land/x/types/http/server.d.ts"
import { serve } from "https://deno.land/std@0.170.0/http/server.ts";

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// Deno and Fetch API type declarations
declare global {
  interface Window {
    fetch: typeof fetch;
  }

  const Deno: {
    env: {
      get(key: string): string | undefined;
    };
  };
}

// Define symbol types
type StockOrCrypto = 'STOCK' | 'CRYPTO';

// Consolidate API keys
const API_KEYS = {
  YOUTUBE: Deno.env.get('YOUTUBE_API_KEY'),
  FINANCIAL: Deno.env.get('FINANCIALDATASETS_API_KEY'),
  GEMINI: Deno.env.get('GEMINI_API_KEY'),
  POLYGON: Deno.env.get('POLYGON_API_KEY')
} as const;

// Helper functions
const normalizeUrl = (url: string): string => url ? (url.startsWith('http') ? url : `https://${url}`) : '';
const isValidUrl = (url: string): boolean => {
  try {
    return !!url && !!new URL(normalizeUrl(url));
  } catch {
    return false;
  }
};

// Add a simple debug logger that won't spam
  const seenMessages = new Set<string>();
  return (message: string, ...args: any[]) => {
    if (!seenMessages.has(message)) {
      seenMessages.add(message);
    }
  };
})();

// Add request logging helper
const logApiRequest = (endpoint: string, params: any) => {
};

// Simplified symbol extraction
const extractSymbols = async (text: string): Promise<{ symbol: string, type: StockOrCrypto }[]> => {
  if (!text) return [];

  try {
    if (!API_KEYS.GEMINI) {
      return [];
    }

    const prompt = `Extract stock and crypto symbols from this text.

Rules:
1. Return ONLY valid stock and crypto symbols
2. For stocks, verify they are currently trading
3. For cryptos, only include major cryptocurrencies
4. ONE symbol maximum - choose the most relevant one
5. No ETFs, futures, or derivatives
6. If the input contains a multi-letter ticker, do NOT return a single-letter ticker UNLESS the input ONLY contains a single-letter ticker.
7. If the input only contains a single-letter ticker, return it.
8. Known single-letter tickers: A (Agilent Technologies), B (Barnes Group), C (Citigroup), D (Dominion Energy), E (Eni S.p.A.), F (Ford Motor Company), G (Genpact Limited), H (Hyatt Hotels), K (Kellogg Company), L (Loews Corporation), M (Macy's), O (Realty Income Corporation), R (Ryder System), T (AT&T), V (Visa), W (Wayfair), X (United States Steel), Y (Alleghany Corporation), Z (Zillow Group)

Input text: "${text}"

Return ONLY a JSON array with ONE object containing:
- symbol: The stock/crypto symbol in uppercase
- type: "STOCK" or "CRYPTO"

Example responses:
For "Tesla stock": [{"symbol": "TSLA", "type": "STOCK"}]
For "Bitcoin price": [{"symbol": "BTC", "type": "CRYPTO"}]
For "Apple earnings": [{"symbol": "AAPL", "type": "STOCK"}]
For "Ford Motor": [{"symbol": "F", "type": "STOCK"}]

Response (JSON array only):`;

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEYS.GEMINI}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{ text: prompt }]
        }],
        tools: [{ googleSearch: {} }], // Enable grounding for symbol verification
        generationConfig: {
          temperature: 0.1,
          maxOutputTokens: 100 // Reduced since we only need a short response
        }
      })
    });

    const responseText = await response.text();

    if (!response.ok) {
      return [];
    }

    const data = JSON.parse(responseText);
    const extractedText = data.candidates?.[0]?.content?.parts?.[0]?.text?.trim() || '';

    // Find the JSON array in the response
    const jsonMatch = extractedText.match(/\[.*\]/s);
    if (!jsonMatch) {
      return [];
    }

    const symbolsData = JSON.parse(jsonMatch[0]);

    if (!Array.isArray(symbolsData)) {
      return [];
    }

    const results = symbolsData
      .map(item => {
        const symbol = item.symbol?.toUpperCase() || '';
        const type: StockOrCrypto = item.type === 'CRYPTO' ? 'CRYPTO' : 'STOCK';
        return { symbol, type } as const;
      })
      .filter(item => item.symbol);

    return results;

  } catch (error) {
    return [];
  }
};

// Update systemPrompt to include news sources
const systemPrompt = `You are Osis, an advanced AI trading assistant powered by Gemini. Analyze the provided market data and research to give insights.
When analyzing:
- Provide detailed analysis incorporating all available data
- Include specific price levels and trends
- Reference fundamental data when available
- Cite news sources and sentiment from Financial Datasets API
- Provide a final summary and trade recommendation
- Maintain a balanced perspective of bullish and bearish factors`;

// Simplified serve function
serve(async (req) => {
  if (req.method === 'OPTIONS') return new Response('ok', { headers: corsHeaders });

  try {
    const { messages } = await req.json();

    const lastMessage = messages?.[messages.length - 1];
    const messageText = typeof lastMessage?.content === 'object'
      ? JSON.stringify(lastMessage.content)
      : lastMessage?.content;

    if (!messageText) throw new Error('Invalid message content');

    // First extract symbols
    const extractedSymbolsData = await extractSymbols(messageText);

    // If no symbols found, indicate this is a general message
    if (extractedSymbolsData.length === 0) {
      return new Response(JSON.stringify({
        symbols: [],
        symbolTypes: {},
        marketData: {},
        isGeneralMessage: true // Add this flag to indicate it's a general message
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }

    // Continue with normal symbol-based analysis if symbols were found
    // Prepare response object
    const response = {
      symbols: extractedSymbolsData.map(s => s.symbol),
      symbolTypes: Object.fromEntries(
        extractedSymbolsData.map(({ symbol, type }) => [symbol, type])
      ),
      marketData: {},
      isGeneralMessage: false // Add this flag to indicate it's not a general message
    };

    // If we have symbols, fetch market data
    if (extractedSymbolsData.length > 0) {
      const marketDataPromises = extractedSymbolsData.map(async ({ symbol, type }) => {
        try {
          const data = await fetchMarketData({ symbol, type });
          return [symbol, data];
        } catch (error) {
          return [symbol, null];
        }
      });

      const marketDataResults = await Promise.all(marketDataPromises);
      response.marketData = Object.fromEntries(marketDataResults.filter(([_, data]) => data !== null));
    }

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});

// Fetch market data based on symbol type
const fetchMarketData = async (symbolData: { symbol: string, type: 'STOCK' | 'CRYPTO' }): Promise<any> => {
  const { symbol, type } = symbolData;
  logApiRequest('Polygon Market Data', { symbol, type });

  try {
    if (type === 'CRYPTO') {
      return await fetchCryptoData(symbol);
    } else {
      return await fetchStockData(symbol);
    }
  } catch (error) {
    return {
      type,
      symbol,
      error: error.message
    };
  }
};

// Fetch stock data from Polygon
const fetchStockData = async (symbol: string): Promise<any> => {
  try {
    const apiKey = API_KEYS.POLYGON;

    // First get the latest quote for the most current price
    const quoteUrl = `https://api.polygon.io/v2/last/trade/${symbol}?apiKey=${apiKey}`;
    const quoteResponse = await fetch(quoteUrl);

    let latestPrice = null;
    if (quoteResponse.ok) {
      const quoteData = await quoteResponse.json();
      if (quoteData.results) {
        latestPrice = quoteData.results.price;
      }
    }

    // Then get historical data
    const historyUrl = `https://api.polygon.io/v2/aggs/ticker/${symbol}/range/1/day/2023-01-01/${new Date().toISOString().split('T')[0]}?apiKey=${apiKey}`;
    const response = await fetch(historyUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch stock data: ${response.status}`);
    }

    const data = await response.json();

    // If we have a latest price, add it to the response
    if (latestPrice !== null && data.results && data.results.length > 0) {
      // Add the latest price to the data
      data.latestPrice = latestPrice;
    }

    return {
      type: 'STOCK',
      symbol: symbol,
      data: data
    };
  } catch (error) {
    return {
      type: 'STOCK',
      symbol: symbol,
      error: error.message
    };
  }
};

// Fetch crypto data from Polygon
const fetchCryptoData = async (symbol: string): Promise<any> => {
  try {
    const apiKey = API_KEYS.POLYGON;

    // Format crypto symbol for Polygon API
    const formattedSymbol = `X:${symbol}USD`;

    // First get the latest quote for the most current price
    const quoteUrl = `https://api.polygon.io/v2/last/trade/${formattedSymbol}?apiKey=${apiKey}`;
    const quoteResponse = await fetch(quoteUrl);

    let latestPrice = null;
    if (quoteResponse.ok) {
      const quoteData = await quoteResponse.json();
      if (quoteData.results) {
        latestPrice = quoteData.results.price;
      }
    }

    // Get date range for historical data
    const today = new Date();
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(today.getFullYear() - 1);

    const startDate = oneYearAgo.toISOString().split('T')[0];
    const endDate = today.toISOString().split('T')[0];

    const url = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/range/1/day/${startDate}/${endDate}?apiKey=${apiKey}`;

    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch crypto data: ${response.status}`);
    }

    const data = await response.json();

    // If we have a latest price, add it to the response
    if (latestPrice !== null && data.results && data.results.length > 0) {
      // Add the latest price to the data
      data.latestPrice = latestPrice;
    }

    return {
      type: 'CRYPTO',
      symbol: symbol,
      formattedSymbol: formattedSymbol,
      data: data
    };
  } catch (error) {
    return {
      type: 'CRYPTO',
      symbol: symbol,
      error: error.message
    };
  }
};
