import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Helper function to ensure Whop user has a complete profile
async function ensureWhopUserProfile(supabaseAdmin: any, supabaseUser: any, whopUser: any) {
  try {
    console.log('👤 Ensuring profile exists for Whop user:', supabaseUser.id)

    // Check if profile already exists
    const { data: existingProfile, error: profileCheckError } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', supabaseUser.id)
      .single()

    if (existingProfile && !profileCheckError) {
      console.log('✅ Profile already exists, updating with Whop data')

      // Update existing profile with Whop data (only update fields that exist)
      const { error: updateError } = await supabaseAdmin
        .from('profiles')
        .update({
          full_name: whopUser.username,
          avatar_url: whopUser.profilePicUrl,
          subscription_type: 'premium', // Ensure Whop users have premium
          updated_at: new Date().toISOString()
        })
        .eq('id', supabaseUser.id)

      if (updateError) {
        console.error('Error updating profile:', updateError)
      } else {
        console.log('✅ Profile updated with Whop data')
      }
    } else {
      console.log('🆕 Creating new profile for Whop user')

      // Use upsert to handle race conditions and foreign key issues
      const { error: upsertError } = await supabaseAdmin
        .from('profiles')
        .upsert({
          id: supabaseUser.id,
          full_name: whopUser.username,
          avatar_url: whopUser.profilePicUrl,
          subscription_type: 'premium', // Whop users get premium automatically
          email: supabaseUser.email,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'id'
        })

      if (upsertError) {
        console.error('Error creating/updating profile:', upsertError)
      } else {
        console.log('✅ Profile created for Whop user')
      }
    }
  } catch (error) {
    console.error('Error ensuring Whop user profile:', error)
    // Don't throw - profile creation is not critical for authentication
  }
}

// Function to set up premium subscription for Whop users
async function setupWhopPremiumSubscription(supabaseAdmin: any, supabaseUser: any) {
  try {
    console.log('🎯 Setting up premium subscription for Whop user:', supabaseUser.id)

    // 1. Update profile with premium subscription type
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .update({
        subscription_type: 'premium',
        updated_at: new Date().toISOString()
      })
      .eq('id', supabaseUser.id)

    if (profileError) {
      console.error('Error updating profile with premium subscription:', profileError)
    } else {
      console.log('✅ Updated profile with premium subscription type')
    }

    // 2. Set up premium token limits (unlimited for Whop users)
    const premiumTokenLimit = 999999; // Very high limit for premium users

    const { error: tokensError } = await supabaseAdmin
      .from('user_tokens')
      .upsert({
        user_id: supabaseUser.id,
        tokens_remaining: premiumTokenLimit,
        last_reset: new Date().toISOString(),
        last_reset_date: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      })

    if (tokensError) {
      console.error('Error setting up premium tokens:', tokensError)
    } else {
      console.log('✅ Set up premium token limits for Whop user')
    }

    console.log('✅ Premium subscription setup complete for Whop user:', {
      userId: supabaseUser.id,
      subscriptionType: 'premium',
      tokenLimit: premiumTokenLimit
    })

  } catch (error) {
    console.error('Error setting up premium subscription for Whop user:', error)
    // Don't throw - this shouldn't block authentication
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase admin client
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { whopUser, accessResult } = await req.json()

    if (!whopUser || !whopUser.id || !whopUser.username) {
      return new Response(
        JSON.stringify({ error: 'Invalid Whop user data' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('🔐 Creating/authenticating Supabase user for Whop user:', {
      whopUserId: whopUser.id,
      username: whopUser.username,
      email: whopUser.email
    })

    // Always use consistent whop.app email format for automatic authentication
    const email = `${whopUser.id}@whop.app`
    const whopAppEmail = email // Store the same email for consistency
    
    // Check if user already exists
    const { data: existingUsers, error: listError } = await supabaseAdmin.auth.admin.listUsers()
    
    if (listError) {
      console.error('Error listing users:', listError)
      throw new Error('Failed to check existing users')
    }

    // Look for existing user by email or by whop_user_id in user_metadata
    let existingUser = existingUsers.users.find(user => 
      user.email === email || 
      user.user_metadata?.whop_user_id === whopUser.id
    )

    let supabaseUser

    if (existingUser) {
      console.log('✅ Found existing Supabase user for Whop user:', existingUser.id)
      supabaseUser = existingUser
      
      // Update user metadata if needed
      const { data: updatedUser, error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
        existingUser.id,
        {
          user_metadata: {
            ...existingUser.user_metadata,
            whop_user_id: whopUser.id,
            username: whopUser.username,
            avatar_url: whopUser.profilePicUrl,
            isWhopUser: true,
            whop_access_level: accessResult?.accessLevel || 'customer',
            whop_company_id: Deno.env.get('WHOP_COMPANY_ID'),
            whop_business_id: 'biz_OGyv6Pz0Le35Fa', // Official Osis business ID
            whop_business_handle: 'tryosis', // Official Osis handle
            last_whop_login: new Date().toISOString()
          }
        }
      )

      if (updateError) {
        console.error('Error updating user metadata:', updateError)
      } else {
        supabaseUser = updatedUser.user
      }
    } else {
      console.log('🆕 Creating new Supabase user for Whop user')
      
      // Create new user
      const { data: newUserData, error: createError } = await supabaseAdmin.auth.admin.createUser({
        email: email,
        email_confirm: true, // Auto-confirm email for Whop users
        user_metadata: {
          whop_user_id: whopUser.id,
          username: whopUser.username,
          full_name: whopUser.username,
          avatar_url: whopUser.profilePicUrl,
          isWhopUser: true,
          whop_access_level: accessResult?.accessLevel || 'customer',
          whop_company_id: Deno.env.get('WHOP_COMPANY_ID'),
          whop_business_id: 'biz_OGyv6Pz0Le35Fa', // Official Osis business ID
          whop_business_handle: 'tryosis', // Official Osis handle
          created_via_whop: true,
          first_whop_login: new Date().toISOString(),
          whop_app_email: whopAppEmail // Store the original whop.app email separately
        }
      })

      if (createError) {
        console.error('Error creating user:', createError)

        // Check if the error is due to user already existing
        if (createError.message?.includes('already been registered') ||
            createError.message?.includes('User already registered')) {
          console.log('🔄 User already exists, attempting to find existing user...')

          // Try to find the existing user by email
          const existingUserByEmail = existingUsers.users.find(user => user.email === email)
          if (existingUserByEmail) {
            console.log('✅ Found existing user by email:', existingUserByEmail.id)
            supabaseUser = existingUserByEmail

            // Update the existing user's metadata
            const { data: updatedUser, error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
              existingUserByEmail.id,
              {
                user_metadata: {
                  ...existingUserByEmail.user_metadata,
                  whop_user_id: whopUser.id,
                  username: whopUser.username,
                  avatar_url: whopUser.profilePicUrl,
                  isWhopUser: true,
                  whop_access_level: accessResult?.accessLevel || 'customer',
                  whop_company_id: Deno.env.get('WHOP_COMPANY_ID'),
                  whop_business_id: 'biz_OGyv6Pz0Le35Fa',
                  whop_business_handle: 'tryosis',
                  last_whop_login: new Date().toISOString()
                }
              }
            )

            if (updateError) {
              console.error('Error updating existing user metadata:', updateError)
            } else {
              supabaseUser = updatedUser.user
              console.log('✅ Updated existing user metadata')
            }
          } else {
            throw new Error(`Failed to create user: ${createError.message}`)
          }
        } else {
          throw new Error(`Failed to create user: ${createError.message}`)
        }
      } else {
        supabaseUser = newUserData.user
        console.log('✅ Created new Supabase user:', supabaseUser.id)
      }

      supabaseUser = newUserData.user
      console.log('✅ Created new Supabase user:', supabaseUser.id)
    }

    // Wait a moment to ensure user is fully created in the database
    await new Promise(resolve => setTimeout(resolve, 100));

    // Ensure the user has a profile with Whop data and premium subscription
    await ensureWhopUserProfile(supabaseAdmin, supabaseUser, whopUser)

    // Set up premium subscription for Whop users (this also updates the profile)
    await setupWhopPremiumSubscription(supabaseAdmin, supabaseUser)

    // Set up password-based authentication using Whop user ID as password
    console.log('🔐 Setting up password authentication for Whop user:', supabaseUser.id)

    try {
      // Use the Whop user ID as the password (user never sees this)
      const password = whopUser.id;

      // Update the user with the password
      const { data: updateData, error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
        supabaseUser.id,
        {
          password: password,
          email_confirm: true // Ensure email is confirmed
        }
      )

      if (updateError) {
        console.error('❌ Failed to set password for Whop user:', updateError)
        return new Response(
          JSON.stringify({
            success: true,
            user: {
              id: supabaseUser.id,
              email: supabaseUser.email,
              user_metadata: supabaseUser.user_metadata
            },
            credentials: null,
            error: 'User created but password setup failed'
          }),
          {
            status: 200,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      console.log('✅ Successfully set up password authentication for Whop user')

      // Return user data with credentials for client-side sign-in
      return new Response(
        JSON.stringify({
          success: true,
          user: {
            id: supabaseUser.id,
            email: supabaseUser.email,
            user_metadata: supabaseUser.user_metadata
          },
          credentials: {
            email: supabaseUser.email,
            password: password
          }
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )

    } catch (passwordSetupError) {
      console.error('❌ Error setting up password authentication:', passwordSetupError)

      // Return user data without credentials
      return new Response(
        JSON.stringify({
          success: true,
          user: {
            id: supabaseUser.id,
            email: supabaseUser.email,
            user_metadata: supabaseUser.user_metadata
          },
          credentials: null,
          error: 'User created but password setup failed'
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

  } catch (error) {
    console.error('❌ Error in whop-user-auth function:', error)

    // Check if this is a "user already exists" error and handle it gracefully
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    if (errorMessage.includes('already been registered') ||
        errorMessage.includes('User already registered')) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to create Supabase user',
          details: 'A user with this email address has already been registered'
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    return new Response(
      JSON.stringify({
        success: false,
        error: errorMessage,
        details: error instanceof Error ? error.stack : undefined
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
