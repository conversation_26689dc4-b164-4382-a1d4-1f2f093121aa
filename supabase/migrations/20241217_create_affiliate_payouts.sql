-- Create affiliate_payouts table to track payouts to Whop community owners
CREATE TABLE IF NOT EXISTS affiliate_payouts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    recipient_username TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT NOT NULL DEFAULT 'usd',
    experience_id TEXT NOT NULL,
    company_id TEXT,
    receipt_id TEXT UNIQUE, -- To prevent duplicate payouts
    original_payment_user_id UUID, -- Reference to the user who made the original payment
    original_payment_amount DECIMAL(10,2), -- Amount of the original payment
    whop_payout_id TEXT, -- ID from <PERSON><PERSON>'s payout system
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed')),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_affiliate_payouts_experience_id ON affiliate_payouts(experience_id);
CREATE INDEX IF NOT EXISTS idx_affiliate_payouts_recipient ON affiliate_payouts(recipient_username);
CREATE INDEX IF NOT EXISTS idx_affiliate_payouts_receipt_id ON affiliate_payouts(receipt_id);
CREATE INDEX IF NOT EXISTS idx_affiliate_payouts_status ON affiliate_payouts(status);
CREATE INDEX IF NOT EXISTS idx_affiliate_payouts_created_at ON affiliate_payouts(created_at);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_affiliate_payouts_updated_at 
    BEFORE UPDATE ON affiliate_payouts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add RLS policies
ALTER TABLE affiliate_payouts ENABLE ROW LEVEL SECURITY;

-- Policy for service role (full access)
CREATE POLICY "Service role can manage affiliate payouts" ON affiliate_payouts
    FOR ALL USING (auth.role() = 'service_role');

-- Policy for authenticated users to view their own community's payouts
CREATE POLICY "Users can view their community payouts" ON affiliate_payouts
    FOR SELECT USING (
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.user_metadata->>'whop_access_level' = 'admin'
            AND auth.users.user_metadata->>'whop_company_id' = company_id
        )
    );

-- Add comments for documentation
COMMENT ON TABLE affiliate_payouts IS 'Tracks affiliate payouts to Whop community owners when their users make payments';
COMMENT ON COLUMN affiliate_payouts.recipient_username IS 'Whop username of the community owner receiving the payout';
COMMENT ON COLUMN affiliate_payouts.amount IS 'Payout amount in the specified currency';
COMMENT ON COLUMN affiliate_payouts.experience_id IS 'Whop experience ID where the original payment was made';
COMMENT ON COLUMN affiliate_payouts.receipt_id IS 'Receipt ID from the original payment to prevent duplicate payouts';
COMMENT ON COLUMN affiliate_payouts.original_payment_user_id IS 'Supabase user ID of the user who made the original payment';
COMMENT ON COLUMN affiliate_payouts.whop_payout_id IS 'ID returned by Whop payout API';
