import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './index.css';
import { initAnalytics, testPostHog } from './utils/analytics';
import { preloadingService } from './services/preloadingService';

// Debug environment variables (removed debug-env import)

// Performance monitoring
const startTime = performance.now();

// Add error boundary with performance tracking
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error to analytics
    if (typeof window !== 'undefined' && window.posthog) {
      window.posthog.capture('app_error', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
      });
    }

    this.setState({ errorInfo });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-[#0A0A0A] text-white p-4">
          <div className="text-center max-w-md">
            <h1 className="text-2xl font-bold mb-4">Something went wrong</h1>
            <p className="text-gray-400 mb-6 text-sm">
              We're sorry for the inconvenience. The error has been logged and we'll fix it soon.
            </p>
            <div className="space-y-3">
              <button
                onClick={() => window.location.reload()}
                className="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-lg font-medium transition-colors"
              >
                Reload App
              </button>
              <button
                onClick={() => {
                  localStorage.clear();
                  sessionStorage.clear();
                  window.location.reload();
                }}
                className="block w-full bg-white/10 hover:bg-white/20 px-6 py-2 rounded-lg font-medium transition-colors"
              >
                Clear Cache & Reload
              </button>
            </div>
            {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
              <details className="mt-6 text-left">
                <summary className="cursor-pointer text-sm text-gray-400 hover:text-white">
                  Error Details (Development)
                </summary>
                <pre className="mt-2 text-xs bg-gray-900 p-3 rounded overflow-auto max-h-40">
                  {this.state.errorInfo.componentStack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Initialize performance optimizations
const initializeApp = async () => {
  try {
    // Initialize analytics
    initAnalytics();

    // Start preloading critical resources
    preloadingService.preloadCriticalResources();

    // Measure app initialization time
    const initTime = performance.now() - startTime;

    // Log performance metrics
    if (typeof window !== 'undefined' && window.posthog) {
      window.posthog.capture('app_init_performance', {
        initTime,
        userAgent: navigator.userAgent,
        connectionType: (navigator as any).connection?.effectiveType || 'unknown',
      });
    }

    // Add PostHog test function to window for development
    if (import.meta.env.DEV && typeof window !== 'undefined') {
      (window as any).testPostHog = testPostHog;

      // Import and make Whop permission fix functions available
      import('./utils/fixWhopPermissions').then(({ fixWhopOwnerPermissions, checkCurrentWhopPermissions }) => {
        (window as any).fixWhopOwnerPermissions = fixWhopOwnerPermissions;
        (window as any).checkCurrentWhopPermissions = checkCurrentWhopPermissions;
      });
    }
  } catch (error) {
  }
};

// Initialize app
initializeApp();

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ErrorBoundary>
      <App />
    </ErrorBoundary>
  </React.StrictMode>
);
