/**
 * Client library for communicating with the Whop Intermediary Server
 * This replaces direct Whop SDK usage in the main Vite app
 */

import { detectCurrentApp } from './whop-app-config';

// Always use the proxy - no exceptions
// This ensures that all requests go through the proxy which forwards Whop user tokens

interface WhopUser {
  id: string;
  username: string;
  email?: string;
  profilePicUrl?: string;
  discordId?: string;
  twitterUsername?: string;
}

interface WhopAccessResult {
  hasAccess: boolean;
  accessLevel: 'no_access' | 'customer' | 'admin';
  userId: string;
  companyId: string;
  membershipInfo?: any;
  timestamp: string;
}

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  user?: WhopUser;
  access?: WhopAccessResult;
  error?: string;
  message?: string;
}

/**
 * Base API call function with error handling and Whop token forwarding
 */
async function apiCall<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    let url: string;
    const currentApp = detectCurrentApp();

    // Always use the main app's proxy endpoint to forward requests with Whop tokens
    // Remove leading slash from endpoint to avoid double encoding
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    // Add app parameter to the endpoint
    const endpointWithApp = cleanEndpoint.includes('?')
      ? `${cleanEndpoint}&app=${currentApp}`
      : `${cleanEndpoint}?app=${currentApp}`;
    url = `/api/whop-proxy?endpoint=${encodeURIComponent(endpointWithApp)}`;

    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      credentials: 'include', // Include cookies for Whop proxy
    });

    // Get response text first to handle both JSON and HTML responses
    const responseText = await response.text();

    // Try to parse as JSON
    let data;
    try {
      data = JSON.parse(responseText);
    } catch (parseError) {
      console.error('❌ Failed to parse response as JSON:', {
        parseError: parseError.message,
        responseText: responseText.substring(0, 1000), // Log first 1000 chars
        contentType: response.headers.get('content-type'),
        status: response.status,
        url: response.url
      });

      // If it's HTML, it's likely a 404 or error page
      if (responseText.includes('<!DOCTYPE') || responseText.includes('<html')) {
        return {
          success: false,
          error: `Received HTML instead of JSON (HTTP ${response.status})`,
          message: `The API endpoint returned an HTML page instead of JSON. This usually means the endpoint doesn't exist or there's a routing issue.`,
          details: {
            status: response.status,
            statusText: response.statusText,
            url: response.url,
            htmlPreview: responseText.substring(0, 500)
          }
        };
      }

      return {
        success: false,
        error: 'Invalid JSON response',
        message: parseError.message,
        details: {
          responseText: responseText.substring(0, 500)
        }
      };
    }

    if (!response.ok) {
      console.error(`❌ API call failed [${response.status}]:`, data);
      return {
        success: false,
        error: data.error || `HTTP ${response.status}`,
        message: data.message
      };
    }

    return data;
  } catch (error) {
    console.error('❌ Network error in API call:', error);
    return {
      success: false,
      error: 'Network error',
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Whop Intermediary Client
 */
export const whopIntermediaryClient = {
  /**
   * Test connection to the intermediary server and Whop API
   */
  async testConnection(): Promise<ApiResponse> {
    return apiCall('/test-connection');
  },

  /**
   * Health check for the intermediary server
   */
  async healthCheck(): Promise<ApiResponse> {
    return apiCall('/health');
  },

  /**
   * Verify a Whop user token
   */
  async verifyUserToken(token: string): Promise<ApiResponse<{ user: WhopUser; verified: boolean }>> {
    return apiCall('/auth/verify', {
      method: 'POST',
      body: JSON.stringify({ token })
    });
  },

  /**
   * Get current Whop user (requires x-whop-user-token header from Whop proxy)
   */
  async getCurrentUser(): Promise<ApiResponse<{ user: WhopUser }>> {
    return apiCall('/user/current');
  },

  /**
   * Test user endpoint (uses agent user for testing without Whop context)
   */
   async getTestUser(): Promise<ApiResponse<{ user: WhopUser }>> {
    return apiCall('/test-user');
  },

  /**
   * Debug headers endpoint to see what headers are being forwarded
   */
  async debugHeaders(): Promise<ApiResponse> {
    return apiCall('/debug-headers');
  },

  /**
   * Test with simulated Whop token (shows what would happen in real Whop context)
   */
  async testWithSimulatedToken(): Promise<ApiResponse> {
    return apiCall('/test-with-token');
  },

  /**
   * Test proxy mode (always uses proxy now)
   */
  async testProxyMode(): Promise<{
    proxy: ApiResponse;
    note: string;
  }> {
    // Always use proxy mode now
    const proxyResult = await this.healthCheck();

    return {
      proxy: proxyResult,
      note: 'Always using proxy mode - direct mode is no longer supported'
    };
  },

  /**
   * Check user access level
   */
  async checkUserAccess(): Promise<ApiResponse<{ access: WhopAccessResult }>> {
    return apiCall('/user/access');
  },

  /**
   * Check user competition creation permissions
   */
  async checkCompetitionPermissions(experienceId: string): Promise<ApiResponse & {
    permissions?: {
      canCreateCompetitions: boolean;
      accessLevel: 'admin' | 'customer' | 'no_access';
      hasAccess: boolean;
      userId?: string;
      experienceId?: string;
      appKey?: string;
      appName?: string;
    }
  }> {
    return apiCall(`/user/competition-permissions?experienceId=${encodeURIComponent(experienceId)}`);
  },

  /**
   * Create/authenticate Supabase user for Whop user
   */
  async createSupabaseUser(whopUser: WhopUser, accessResult?: WhopAccessResult): Promise<ApiResponse<{
    user: any;
    credentials: { email: string; password: string };
    isNewUser: boolean;
  }>> {
    return apiCall('/auth/supabase', {
      method: 'POST',
      body: JSON.stringify({ whopUser, accessResult })
    });
  },

  /**
   * Get multi-app configuration status
   */
  async getMultiAppStatus(): Promise<ApiResponse> {
    return apiCall('/multi-app-status');
  },

  /**
   * Initialize Whop authentication (combines user verification and access check)
   */
  async initializeAuth(): Promise<{
    isWhopUser: boolean;
    user: WhopUser | null;
    accessResult: WhopAccessResult | null;
    error?: string;
  }> {
    try {
      // Always use proxy - get current user through proxy which forwards Whop tokens
      const userResponse = await this.getCurrentUser();

      if (!userResponse.success || !userResponse.user) {
        console.log('ℹ️ No Whop user found or not in Whop context');

        return {
          isWhopUser: false,
          user: null,
          accessResult: null,
          error: userResponse.error
        };
      }

      const user = userResponse.user;
      console.log('✅ Whop user found:', user.username);

      // Check user access
      const accessResponse = await this.checkUserAccess();
      
      if (!accessResponse.success) {
        console.warn('⚠️ Could not check user access:', accessResponse.error);
        // Still return user data even if access check fails
        return {
          isWhopUser: true,
          user,
          accessResult: null,
          error: accessResponse.error
        };
      }

      const accessResult = accessResponse.access!;
      console.log('✅ Access check completed:', {
        hasAccess: accessResult.hasAccess,
        accessLevel: accessResult.accessLevel
      });

      return {
        isWhopUser: true,
        user,
        accessResult
      };

    } catch (error) {
      console.error('❌ Error initializing Whop auth:', error);

      return {
        isWhopUser: false,
        user: null,
        accessResult: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  /**
   * Helper method to get experience ID from various sources
   */
  getExperienceIdFromContext(): string | null {
    if (typeof window === 'undefined') return null;

    // Method 1: URL path (original Whop iframe)
    const pathMatch = window.location.pathname.match(/\/experiences\/([^\/]+)/);
    if (pathMatch) {
      return pathMatch[1];
    }

    // Method 2: URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const experienceParam = urlParams.get('experience') || urlParams.get('experienceId') || urlParams.get('exp');
    if (experienceParam) {
      return experienceParam;
    }

    // Method 3: URL hash
    const hashParams = new URLSearchParams(window.location.hash.substring(1));
    const hashExperience = hashParams.get('experience') || hashParams.get('experienceId') || hashParams.get('exp');
    if (hashExperience) {
      return hashExperience;
    }

    // Method 4: localStorage
    try {
      const stored = localStorage.getItem('whop_experience_id');
      if (stored) return stored;
    } catch {
      // Ignore localStorage errors
    }

    return null;
  },

  /**
   * Create a Whop charge for a user (server-side operation via intermediary)
   */
  async createCharge(amount: number, currency: string = 'usd', description?: string, metadata?: Record<string, any>): Promise<ApiResponse<{
    inAppPurchase: {
      id: string;
      planId: string;
    };
    status: 'needs_action' | 'success';
  }>> {
    return apiCall('/whop/charge', {
      method: 'POST',
      body: JSON.stringify({ amount, currency, description, metadata })
    });
  },

  /**
   * Send affiliate payout to Whop owner
   */
  async sendAffiliatePayout(recipientUsername: string, amount: number, experienceId: string): Promise<ApiResponse<{
    success: boolean;
    payoutId?: string;
  }>> {
    return apiCall('/whop/affiliate-payout', {
      method: 'POST',
      body: JSON.stringify({ recipientUsername, amount, experienceId })
    });
  },

  /**
   * Get community analytics for Whop owners
   */
  async getCommunityAnalytics(experienceId: string): Promise<ApiResponse<{
    totalSignups: number;
    totalEarnings: number;
    recentSignups: Array<{
      username: string;
      signupDate: string;
      amount: number;
    }>;
  }>> {
    return apiCall(`/whop/community-analytics?experienceId=${encodeURIComponent(experienceId)}`);
  },

  /**
   * Generic API call method for making requests to the intermediary server
   */
  async apiCall<T = any>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    return apiCall<T>(endpoint, options);
  }
};

// Export types for use in other files
export type { WhopUser, WhopAccessResult, ApiResponse };
