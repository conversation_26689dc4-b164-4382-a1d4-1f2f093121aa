/**
 * Official Whop iframe SDK integration
 * Uses the official @whop/iframe package
 */

import { createSdk } from "@whop/iframe";

// Get the appropriate app ID based on current path
const getAppId = () => {
  if (typeof window === 'undefined') return import.meta.env.VITE_WHOP_APP_ID;

  const path = window.location.pathname;
  if (path.includes('/trade')) {
    return import.meta.env.VITE_TRADING_WHOP_APP_ID || import.meta.env.VITE_WHOP_APP_ID;
  }
  return import.meta.env.VITE_WHOP_APP_ID;
};

// Create the official iframe SDK instance
export const iframeSdk = createSdk({
  appId: getAppId(),
});

// Console logging removed

// Check if SDK is properly initialized
// Console logging removed

// Automatic payment flow has been removed - payment is now handled through the welcome modal
// The iframe SDK is still available for manual payment processing when triggered by user action


