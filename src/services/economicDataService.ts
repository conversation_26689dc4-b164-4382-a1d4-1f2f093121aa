import { ChartDataPoint } from '@/components/charts/SocialMediaChartGenerator';

// Types for economic and fundamental data
export interface CompanyFinancials {
  symbol: string;
  revenue: ChartDataPoint[];
  netIncome: ChartDataPoint[];
  grossProfit: ChartDataPoint[];
  operatingIncome: ChartDataPoint[];
  totalAssets: ChartDataPoint[];
  totalDebt: ChartDataPoint[];
  freeCashFlow: ChartDataPoint[];
  dividends: ChartDataPoint[];
}

export interface EconomicIndicator {
  name: string;
  data: ChartDataPoint[];
  unit: string;
  source: string;
}

export interface SectorData {
  sectorName: string;
  companies: string[];
  metrics: {
    [metricName: string]: ChartDataPoint[];
  };
}

/**
 * Fetch company fundamentals from Polygon API
 */
export async function fetchCompanyFinancials(symbol: string): Promise<CompanyFinancials> {
  const apiKey = import.meta.env.VITE_POLYGON_API_KEY;
  if (!apiKey) {
    return generateSampleFinancialData(symbol);
  }

  try {
    // Fetch financials data from Polygon
    const response = await fetch(
      `https://api.polygon.io/vX/reference/financials?ticker=${symbol}&limit=20&apikey=${apiKey}`
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch financials for ${symbol}`);
    }

    const data = await response.json();
    
    if (!data.results || data.results.length === 0) {
      throw new Error(`No financial data found for ${symbol}`);
    }

    // Transform the data into chart format
    const financials: CompanyFinancials = {
      symbol,
      revenue: [],
      netIncome: [],
      grossProfit: [],
      operatingIncome: [],
      totalAssets: [],
      totalDebt: [],
      freeCashFlow: [],
      dividends: []
    };

    // Process each financial report
    data.results.forEach((report: any) => {
      const period = report.end_date;
      const periodName = formatPeriodName(period);
      
      // Extract key metrics
      const income = report.financials?.income_statement;
      const balance = report.financials?.balance_sheet;
      const cashFlow = report.financials?.cash_flow_statement;

      if (income) {
        if (income.revenues?.value) {
          financials.revenue.push({
            name: periodName,
            value: income.revenues.value / 1000000, // Convert to millions
            date: period
          });
        }

        if (income.net_income_loss?.value) {
          financials.netIncome.push({
            name: periodName,
            value: income.net_income_loss.value / 1000000,
            date: period
          });
        }

        if (income.gross_profit?.value) {
          financials.grossProfit.push({
            name: periodName,
            value: income.gross_profit.value / 1000000,
            date: period
          });
        }

        if (income.operating_income_loss?.value) {
          financials.operatingIncome.push({
            name: periodName,
            value: income.operating_income_loss.value / 1000000,
            date: period
          });
        }
      }

      if (balance) {
        if (balance.assets?.value) {
          financials.totalAssets.push({
            name: periodName,
            value: balance.assets.value / 1000000,
            date: period
          });
        }

        if (balance.liabilities?.value) {
          financials.totalDebt.push({
            name: periodName,
            value: balance.liabilities.value / 1000000,
            date: period
          });
        }
      }

      if (cashFlow) {
        if (cashFlow.net_cash_flow_from_operating_activities?.value) {
          financials.freeCashFlow.push({
            name: periodName,
            value: cashFlow.net_cash_flow_from_operating_activities.value / 1000000,
            date: period
          });
        }
      }
    });

    // Sort all arrays by date (most recent first)
    Object.keys(financials).forEach(key => {
      if (key !== 'symbol' && Array.isArray(financials[key as keyof CompanyFinancials])) {
        (financials[key as keyof CompanyFinancials] as ChartDataPoint[]).sort((a, b) => 
          new Date(a.date || '').getTime() - new Date(b.date || '').getTime()
        );
      }
    });

    return financials;

  } catch (error) {
    throw error;
  }
}

/**
 * Fetch dividend data for a company
 */
export async function fetchDividendData(symbol: string): Promise<ChartDataPoint[]> {
  const apiKey = import.meta.env.VITE_POLYGON_API_KEY;
  if (!apiKey) {
    return generateSampleDividendData(symbol);
  }

  try {
    const response = await fetch(
      `https://api.polygon.io/v3/reference/dividends?ticker=${symbol}&limit=50&apikey=${apiKey}`
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch dividend data for ${symbol}`);
    }

    const data = await response.json();
    
    if (!data.results || data.results.length === 0) {
      return [];
    }

    return data.results.map((dividend: any) => ({
      name: formatPeriodName(dividend.ex_dividend_date),
      value: dividend.cash_amount || 0,
      date: dividend.ex_dividend_date
    })).sort((a: ChartDataPoint, b: ChartDataPoint) => 
      new Date(a.date || '').getTime() - new Date(b.date || '').getTime()
    );

  } catch (error) {
    return [];
  }
}

/**
 * Calculate financial ratios and growth metrics
 */
export function calculateFinancialMetrics(financials: CompanyFinancials) {
  const metrics = {
    revenueGrowth: calculateGrowthRate(financials.revenue),
    netIncomeGrowth: calculateGrowthRate(financials.netIncome),
    grossMargin: calculateMargin(financials.grossProfit, financials.revenue),
    operatingMargin: calculateMargin(financials.operatingIncome, financials.revenue),
    netMargin: calculateMargin(financials.netIncome, financials.revenue)
  };

  return metrics;
}

/**
 * Helper function to calculate growth rate between periods
 */
function calculateGrowthRate(data: ChartDataPoint[]): ChartDataPoint[] {
  if (data.length < 2) return [];

  const growthData: ChartDataPoint[] = [];
  
  for (let i = 1; i < data.length; i++) {
    const current = data[i].value;
    const previous = data[i - 1].value;
    
    if (previous !== 0) {
      const growthRate = ((current - previous) / Math.abs(previous)) * 100;
      growthData.push({
        name: data[i].name,
        value: growthRate,
        date: data[i].date
      });
    }
  }

  return growthData;
}

/**
 * Helper function to calculate margin ratios
 */
function calculateMargin(numerator: ChartDataPoint[], denominator: ChartDataPoint[]): ChartDataPoint[] {
  const marginData: ChartDataPoint[] = [];
  
  numerator.forEach(numItem => {
    const denomItem = denominator.find(d => d.date === numItem.date);
    if (denomItem && denomItem.value !== 0) {
      marginData.push({
        name: numItem.name,
        value: (numItem.value / denomItem.value) * 100,
        date: numItem.date
      });
    }
  });

  return marginData;
}

/**
 * Format period name for display
 */
function formatPeriodName(dateString: string): string {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = date.getMonth();
  
  // Determine quarter
  const quarter = Math.floor(month / 3) + 1;
  
  return `Q${quarter} '${year.toString().slice(-2)}`;
}

/**
 * Generate realistic sample financial data for a given symbol
 */
export function generateSampleFinancialData(symbol: string): CompanyFinancials {
  // Different sample data patterns based on symbol
  const patterns = {
    'AAPL': { baseRevenue: 90000, growth: 0.08, volatility: 0.1 },
    'MSFT': { baseRevenue: 50000, growth: 0.12, volatility: 0.08 },
    'GOOGL': { baseRevenue: 70000, growth: 0.10, volatility: 0.12 },
    'AMZN': { baseRevenue: 130000, growth: 0.15, volatility: 0.15 },
    'TSLA': { baseRevenue: 25000, growth: 0.25, volatility: 0.25 },
    'META': { baseRevenue: 30000, growth: 0.18, volatility: 0.20 },
    'NVDA': { baseRevenue: 15000, growth: 0.35, volatility: 0.30 },
    'NFLX': { baseRevenue: 8000, growth: 0.12, volatility: 0.18 }
  };

  const pattern = patterns[symbol as keyof typeof patterns] || { baseRevenue: 10000, growth: 0.10, volatility: 0.15 };

  const quarters = [
    'Q1 \'22', 'Q2 \'22', 'Q3 \'22', 'Q4 \'22',
    'Q1 \'23', 'Q2 \'23', 'Q3 \'23', 'Q4 \'23',
    'Q1 \'24', 'Q2 \'24', 'Q3 \'24', 'Q4 \'24'
  ];

  const dates = [
    '2022-03-31', '2022-06-30', '2022-09-30', '2022-12-31',
    '2023-03-31', '2023-06-30', '2023-09-30', '2023-12-31',
    '2024-03-31', '2024-06-30', '2024-09-30', '2024-12-31'
  ];

  const revenue: ChartDataPoint[] = [];
  const netIncome: ChartDataPoint[] = [];
  const grossProfit: ChartDataPoint[] = [];
  const operatingIncome: ChartDataPoint[] = [];

  let currentRevenue = pattern.baseRevenue;

  quarters.forEach((quarter, index) => {
    // Add some realistic growth with volatility
    const growthFactor = 1 + pattern.growth / 4; // Quarterly growth
    const volatilityFactor = 1 + (Math.random() - 0.5) * pattern.volatility;
    currentRevenue *= growthFactor * volatilityFactor;

    const revenueValue = Math.round(currentRevenue);
    const netIncomeValue = Math.round(revenueValue * (0.15 + Math.random() * 0.1)); // 15-25% net margin
    const grossProfitValue = Math.round(revenueValue * (0.35 + Math.random() * 0.15)); // 35-50% gross margin
    const operatingIncomeValue = Math.round(revenueValue * (0.20 + Math.random() * 0.1)); // 20-30% operating margin

    revenue.push({ name: quarter, value: revenueValue, date: dates[index] });
    netIncome.push({ name: quarter, value: netIncomeValue, date: dates[index] });
    grossProfit.push({ name: quarter, value: grossProfitValue, date: dates[index] });
    operatingIncome.push({ name: quarter, value: operatingIncomeValue, date: dates[index] });
  });

  return {
    symbol,
    revenue,
    netIncome,
    grossProfit,
    operatingIncome,
    totalAssets: [],
    totalDebt: [],
    freeCashFlow: [],
    dividends: []
  };
}

/**
 * Generate sample dividend data
 */
function generateSampleDividendData(symbol: string): ChartDataPoint[] {
  // Companies that typically pay dividends
  const dividendPayers = ['AAPL', 'MSFT', 'JNJ', 'PG', 'KO', 'PEP', 'WMT', 'HD'];

  if (!dividendPayers.includes(symbol)) {
    return []; // Non-dividend paying stock
  }

  const baseDividend = {
    'AAPL': 0.24,
    'MSFT': 0.75,
    'JNJ': 1.13,
    'PG': 0.91,
    'KO': 0.46,
    'PEP': 1.15,
    'WMT': 0.57,
    'HD': 1.90
  }[symbol] || 0.50;

  const quarters = ['Q1 \'23', 'Q2 \'23', 'Q3 \'23', 'Q4 \'23', 'Q1 \'24', 'Q2 \'24', 'Q3 \'24', 'Q4 \'24'];
  const dates = ['2023-03-15', '2023-06-15', '2023-09-15', '2023-12-15', '2024-03-15', '2024-06-15', '2024-09-15', '2024-12-15'];

  return quarters.map((quarter, index) => ({
    name: quarter,
    value: baseDividend * (1 + index * 0.02), // Small dividend growth
    date: dates[index]
  }));
}

/**
 * Create sample data for testing
 */
export function createSampleFinancialData(): CompanyFinancials {
  return generateSampleFinancialData('SAMPLE');
}
