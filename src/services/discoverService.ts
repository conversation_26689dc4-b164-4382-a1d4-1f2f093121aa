import { supabase } from '@/integrations/supabase/client';

// Types for the discover service
export interface PublishedAgent {
  id: string;
  agentId: string;
  name: string;
  description?: string;
  category: string;
  tags: string[];
  publisherName: string;
  publisherId: string;
  downloadCount: number;
  averageRating: number;
  totalReviews: number;
  isFeatured: boolean;
  createdAt: string;
  updatedAt: string;
  configuration?: any;
}

export interface AgentCategory {
  id: string;
  name: string;
  description?: string;
  icon_url?: string;
  sort_order: number;
  is_active: boolean;
}

export interface AgentReview {
  id: string;
  rating: number;
  reviewText?: string;
  reviewerName: string;
  reviewerId: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface DiscoverFilters {
  search?: string;
  category?: string;
  tags?: string[];
  sortBy?: 'newest' | 'popular' | 'rating' | 'downloads';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
  featured?: boolean;
}

export interface DiscoverResponse {
  success: boolean;
  agents: PublishedAgent[];
  categories: AgentCategory[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  error?: string;
}

export interface PublishAgentRequest {
  agentId: string;
  name: string;
  description?: string;
  category: string;
  tags?: string[];
}

export interface ImportAgentRequest {
  publishedAgentId: string;
  customName?: string;
}

export interface CreateReviewRequest {
  publishedAgentId: string;
  rating: number;
  reviewText?: string;
}

export interface UpdateReviewRequest {
  reviewId: string;
  rating: number;
  reviewText?: string;
}

/**
 * Get FREE marketplace agents ONLY (strict isolation)
 * This function ONLY returns agents from published_agents table with is_active=true
 * It will NEVER return paid agents that are for sale
 */
export async function getFreeMarketplaceAgents(filters: DiscoverFilters = {}): Promise<DiscoverResponse> {
  try {
    // STRICT ISOLATION: Only get free agents from published_agents table
    // Must have: is_active = true in published_agents
    // Must NOT have: is_for_sale = true in agents table (to prevent cross-contamination)
    const { data, error } = await supabase.functions.invoke('discover-agents', {
      body: {
        ...filters,
        strict_free_only: true // Flag to enforce strict isolation in edge function
      }
    });

    if (error) {
      return {
        success: false,
        agents: [],
        categories: [],
        pagination: { total: 0, limit: 0, offset: 0, hasMore: false },
        error: error.message || 'Failed to get free marketplace agents'
      };
    }

    return data;
  } catch (error) {
    return {
      success: false,
      agents: [],
      categories: [],
      pagination: { total: 0, limit: 0, offset: 0, hasMore: false },
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Discover published agents with filtering and search
 * DEPRECATED: Use getFreeMarketplaceAgents() for strict isolation
 */
export async function discoverAgents(filters: DiscoverFilters = {}): Promise<DiscoverResponse> {
  try {
    const { data, error } = await supabase.functions.invoke('discover-agents', {
      body: filters
    });

    if (error) {
      return {
        success: false,
        agents: [],
        categories: [],
        pagination: { total: 0, limit: 0, offset: 0, hasMore: false },
        error: error.message || 'Failed to discover agents'
      };
    }

    return data;
  } catch (error) {
    return {
      success: false,
      agents: [],
      categories: [],
      pagination: { total: 0, limit: 0, offset: 0, hasMore: false },
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Publish an agent to the marketplace
 */
export async function publishAgent(request: PublishAgentRequest): Promise<{ success: boolean; publishedAgent?: any; message?: string; error?: string }> {
  try {
    const { data, error } = await supabase.functions.invoke('publish-agent', {
      body: request
    });

    if (error) {
      return {
        success: false,
        error: error.message || 'Failed to publish agent'
      };
    }

    return data;
  } catch (error) {
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Unpublish an agent from the free marketplace
 */
export async function unpublishAgent(agentId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Set is_active to false for the published agent
    const { data, error } = await supabase
      .from('published_agents')
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('agent_id', agentId)
      .eq('publisher_id', user.id)
      .select();

    if (error) {
      throw error;
    }

    if (!data || data.length === 0) {
      return {
        success: false,
        error: 'Agent not found or you do not have permission to unpublish it'
      };
    }

    // SYNCHRONIZATION FIX: Check if agent should remain public (if it's for sale)
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('is_for_sale, is_public')
      .eq('id', agentId)
      .single();

    if (!agentError && agent && !agent.is_for_sale && agent.is_public) {
      // Agent is not for sale and is currently public, so make it private
      const { error: updateError } = await supabase
        .from('agents')
        .update({
          is_public: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', agentId)
        .eq('user_id', user.id);

      if (updateError) {
        // Don't fail the whole operation
      } else {
        // Don't fail the whole operation
      }
    } else if (!agentError && agent && agent.is_for_sale) {
      // Don't fail the whole operation
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Failed to unpublish agent'
    };
  }
}

/**
 * Import a published agent to user's library
 */
export async function importAgent(request: ImportAgentRequest): Promise<{ success: boolean; agent?: any; message?: string; alreadyImported?: boolean; error?: string }> {
  try {
    const { data, error } = await supabase.functions.invoke('import-agent', {
      body: request
    });

    if (error) {
      return {
        success: false,
        error: error.message || 'Failed to import agent'
      };
    }

    return data;
  } catch (error) {
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Get reviews for a published agent
 */
export async function getAgentReviews(publishedAgentId: string): Promise<{ success: boolean; reviews: AgentReview[]; error?: string }> {
  try {
    const { data, error } = await supabase.functions.invoke('agent-reviews', {
      method: 'GET',
      body: { publishedAgentId }
    });

    if (error) {
      return {
        success: false,
        reviews: [],
        error: error.message || 'Failed to get agent reviews'
      };
    }

    return data;
  } catch (error) {
    return {
      success: false,
      reviews: [],
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Create a review for a published agent
 */
export async function createAgentReview(request: CreateReviewRequest): Promise<{ success: boolean; review?: any; message?: string; error?: string }> {
  try {
    const { data, error } = await supabase.functions.invoke('agent-reviews', {
      method: 'POST',
      body: request
    });

    if (error) {
      return {
        success: false,
        error: error.message || 'Failed to create review'
      };
    }

    return data;
  } catch (error) {
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Update an existing review
 */
export async function updateAgentReview(request: UpdateReviewRequest): Promise<{ success: boolean; review?: any; message?: string; error?: string }> {
  try {
    const { data, error } = await supabase.functions.invoke('agent-reviews', {
      method: 'PUT',
      body: request
    });

    if (error) {
      return {
        success: false,
        error: error.message || 'Failed to update review'
      };
    }

    return data;
  } catch (error) {
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Delete a review
 */
export async function deleteAgentReview(reviewId: string): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    const { data, error } = await supabase.functions.invoke('agent-reviews', {
      method: 'DELETE',
      body: { reviewId }
    });

    if (error) {
      return {
        success: false,
        error: error.message || 'Failed to delete review'
      };
    }

    return data;
  } catch (error) {
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Check if an agent is currently in the marketplace
 * This checks both the published_agents table (for free agents) and agents table (for paid agents)
 */
export async function checkAgentMarketplaceStatus(agentId: string): Promise<{
  isInMarketplace: boolean;
  isPublished: boolean;
  isForSale: boolean;
  error?: string;
}> {
  try {
    // Check if agent is published in the free marketplace (published_agents table)
    const { data: publishedAgent, error: publishedError } = await supabase
      .from('published_agents')
      .select('id, is_active')
      .eq('agent_id', agentId)
      .eq('is_active', true)
      .single();

    const isPublished = !publishedError && !!publishedAgent;

    // Check if agent is for sale in the paid marketplace (agents table)
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('is_for_sale, is_public')
      .eq('id', agentId)
      .single();

    const isForSale = !agentError && agent?.is_for_sale === true && agent?.is_public === true;

    // Agent is in marketplace if it's either published (free) or for sale (paid)
    const isInMarketplace = isPublished || isForSale;

    return {
      isInMarketplace,
      isPublished: !!isPublished,
      isForSale: !!isForSale
    };
  } catch (error) {
    return {
      isInMarketplace: false,
      isPublished: false,
      isForSale: false,
      error: error.message || 'Failed to check marketplace status'
    };
  }
}
