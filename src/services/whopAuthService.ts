import { supabase } from '@/integrations/supabase/client';
import { WhopUser } from '@/utils/whopAuth';

export interface WhopSupabaseUser {
  id: string;
  email: string;
  user_metadata: {
    whop_user_id: string;
    username: string;
    full_name: string;
    avatar_url?: string;
    isWhopUser: boolean;
    [key: string]: any;
  };
}

export interface WhopAuthResult {
  success: boolean;
  user?: WhopSupabaseUser;
  session?: any;
  credentials?: {
    email: string;
    password: string;
  };
  error?: string;
}

/**
 * Create or authenticate a Supabase user for a Whop user
 * This allows Whop users to access backend services that require Supabase authentication
 */
export const createWhopSupabaseUser = async (whopUser: WhopUser): Promise<WhopAuthResult> => {
  try {
    // Call the Supabase function to create/authenticate the user
    const { data, error } = await supabase.functions.invoke('whop-user-auth', {
      body: { whopUser }
    });

    if (error) {
      return {
        success: false,
        error: error.message || 'Failed to authenticate Whop user'
      };
    }

    if (!data.success) {
      return {
        success: false,
        error: data.error || 'Authentication failed'
      };
    }

    // If we got credentials, sign in with password to create a session
    if (data.credentials && data.credentials.email && data.credentials.password) {
      try {
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: data.credentials.email,
          password: data.credentials.password
        });

        if (signInError || !signInData.session) {
          return {
            success: true,
            user: data.user,
            session: null,
            error: 'Credentials received but sign-in failed: ' + (signInError?.message || 'Unknown error')
          };
        }

        // Verify the session is working
        const { data: { user: verifyUser }, error: verifyError } = await supabase.auth.getUser();
        if (verifyUser) {
        } else {
        }

        return {
          success: true,
          user: data.user,
          session: signInData.session
        };

      } catch (signInError) {
        return {
          success: true,
          user: data.user,
          session: null,
          error: 'Credentials received but sign-in failed'
        };
      }
    }

    // If we got a session directly (backward compatibility), set it in the client
    if (data.session && data.session.access_token) {
      const { error: setSessionError } = await supabase.auth.setSession({
        access_token: data.session.access_token,
        refresh_token: data.session.refresh_token
      });

      if (setSessionError) {
      } else {
      }

      return {
        success: true,
        user: data.user,
        session: data.session
      };
    }

    // If we reach here, user was created but no session/credentials were provided
    return {
      success: true,
      user: data.user,
      session: null,
      error: 'User created but no authentication method available'
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};



/**
 * Check if a Whop user already has a Supabase session
 */
export const checkWhopSupabaseSession = async (): Promise<boolean> => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      return false;
    }

    // Check if this is a Whop user session
    const isWhopUser = session?.user?.user_metadata?.isWhopUser === true;
    
    if (session && isWhopUser) {
      return true;
    }

    return false;
  } catch (error) {
    return false;
  }
};

/**
 * Get the current Supabase user if they're a Whop user
 * This function now includes a fallback to look up Whop users who exist in Supabase
 * but don't have an active session
 */
export const getCurrentWhopSupabaseUser = async (): Promise<WhopSupabaseUser | null> => {
  try {
    // First, try to get the current session user
    const { data: { user }, error } = await supabase.auth.getUser();

    if (!error && user && user.user_metadata?.isWhopUser === true) {
      return {
        id: user.id,
        email: user.email!,
        user_metadata: user.user_metadata as WhopSupabaseUser['user_metadata']
      };
    }

    // If no active session, check if we have Whop user data in localStorage
    // and try to find the corresponding Supabase user
    const whopUserData = localStorage.getItem('whop_user_data');
    if (whopUserData) {
      try {
        const whopUser = JSON.parse(whopUserData);

        // Call the lookup function to find the Whop user in Supabase
        const { data: lookupResult, error: lookupError } = await supabase.functions.invoke('whop-user-lookup', {
          body: { whopUserId: whopUser.id }
        });

        if (!lookupError && lookupResult?.user) {
          return {
            id: lookupResult.user.id,
            email: lookupResult.user.email,
            user_metadata: lookupResult.user.user_metadata as WhopSupabaseUser['user_metadata']
          };
        } else {
        }
      } catch (parseError) {
      }
    }

    return null;
  } catch (error) {
    return null;
  }
};
