import { supabase } from '@/integrations/supabase/client';

export interface ChartDataParams {
  symbol: string;
  timeframe: '1D' | '1W' | '1M' | '3M' | '6M' | '1Y' | '5Y';
  interval?: string;
}

export async function fetchChartData({ symbol, timeframe, interval }: ChartDataParams) {
  try {
    const { data, error } = await supabase.functions.invoke('chart-data', {
      body: JSON.stringify({
        symbol,
        timeframe,
        interval: interval || getDefaultInterval(timeframe)
      })
    });

    if (error) throw error;
    return data;
  } catch (error) {
    throw error;
  }
}

function getDefaultInterval(timeframe: string): string {
  switch (timeframe) {
    case '1D': return '5min';
    case '1W': return '30min';
    case '1M': return '1hour';
    case '3M': return 'daily';
    case '6M': return 'daily';
    case '1Y': return 'daily';
    case '5Y': return 'weekly';
    default: return 'daily';
  }
} 