import { supabase } from '@/integrations/supabase/client';
import { getAuthenticatedUser, ensureAuthenticated } from './authService';

export interface WhopDistributionRequest {
  agentId: string;
  whopCompanyId: string;
  whopExperienceId?: string;
}

export interface WhopDistributionResult {
  success: boolean;
  message?: string;
  distributionId?: string;
  membersAdded?: number;
  errors?: string[];
  error?: string;
}

export interface WhopDistributedAgent {
  id: string;
  agent_id: string;
  distributor_id: string;
  whop_company_id: string;
  whop_experience_id?: string;
  distributed_at: string;
  is_active: boolean;
  agent?: {
    id: string;
    name: string;
    description?: string;
  };
}

export interface WhopMemberAgent {
  id: string;
  whop_distribution_id: string;
  member_user_id: string;
  whop_member_id: string;
  agent_id: string;
  custom_name?: string;
  added_at: string;
  is_active: boolean;
  agent?: {
    id: string;
    name: string;
    description?: string;
    configuration: any;
  };
}

/**
 * Distribute an agent to all Whop company members
 */
export async function distributeAgentToWhopMembers(
  request: WhopDistributionRequest
): Promise<WhopDistributionResult> {
  try {
    // Ensure we have proper authentication with session
    const { user, session } = await ensureAuthenticated();

    // Verify user is a Whop user
    if (!user.user_metadata?.whop_user_id) {
      throw new Error('Only Whop users can distribute agents to Whop members');
    }

    // Call the distribution function
    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/whop-distribute-agent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `Server returned ${response.status}`);
    }

    const result = await response.json();

    return result;
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to distribute agent'
    };
  }
}

/**
 * Get distributed agents for the current user's Whop company
 */
export async function getWhopDistributedAgents(
  whopCompanyId: string
): Promise<WhopDistributedAgent[]> {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      throw new Error('Authentication required');
    }

    const { data, error } = await supabase
      .from('whop_distributed_agents')
      .select(`
        *,
        agent:agents(id, name, description)
      `)
      .eq('whop_company_id', whopCompanyId)
      .eq('is_active', true)
      .order('distributed_at', { ascending: false });

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    return [];
  }
}

/**
 * Get agents distributed to the current user from Whop
 */
export async function getWhopMemberAgents(): Promise<WhopMemberAgent[]> {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      throw new Error('Authentication required');
    }

    const { data, error } = await supabase
      .from('whop_member_agents')
      .select(`
        *,
        agent:agents(id, name, description, configuration)
      `)
      .eq('member_user_id', user.id)
      .eq('is_active', true)
      .order('added_at', { ascending: false });

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    return [];
  }
}

/**
 * Remove agent distribution (only for distributors)
 */
export async function removeWhopAgentDistribution(
  distributionId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      throw new Error('Authentication required');
    }

    // Deactivate the distribution
    const { error } = await supabase
      .from('whop_distributed_agents')
      .update({ is_active: false })
      .eq('id', distributionId)
      .eq('distributor_id', user.id);

    if (error) {
      throw error;
    }

    // Deactivate all member distributions
    const { error: memberError } = await supabase
      .from('whop_member_agents')
      .update({ is_active: false })
      .eq('whop_distribution_id', distributionId);

    if (memberError) {
      console.warn('Warning: Failed to deactivate some member distributions:', memberError);
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to remove distribution'
    };
  }
}

/**
 * Check if user can distribute agents (is Whop admin)
 */
export async function canDistributeToWhopMembers(
  whopCompanyId: string
): Promise<{ canDistribute: boolean; reason?: string }> {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      return { canDistribute: false, reason: 'Authentication required' };
    }

    // Check if user is a Whop user
    if (!user.user_metadata?.whop_user_id) {
      return { canDistribute: false, reason: 'Only Whop users can distribute agents' };
    }

    // Check if user has admin access level
    if (!user.user_metadata?.whop_access_level || user.user_metadata.whop_access_level !== 'admin') {
      return { canDistribute: false, reason: 'Only Whop admins can distribute agents to all members' };
    }

    return { canDistribute: true };
  } catch (error) {
    return { canDistribute: false, reason: 'Failed to verify permissions' };
  }
}
