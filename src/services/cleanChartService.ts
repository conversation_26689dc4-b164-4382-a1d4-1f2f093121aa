import { format, subHours, addHours } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';
import { FocusedOHLCVData } from './tradeAnalysisService';

/**
 * Remove gaps between trading sessions to create a continuous timeline
 * This compresses weekends and overnight gaps for better chart visualization
 */
function removeTradingGaps(data: FocusedOHLCVData[], timeframe: TradeTimeframe = '15min'): FocusedOHLCVData[] {
  if (data.length === 0) return data;

  // For daily timeframes and above, don't compress gaps as they represent normal trading day intervals
  if (timeframe === 'daily') {
    return data;
  }

  // Sort data by timestamp to ensure proper order
  const sortedData = [...data].sort((a, b) => a.timestamp - b.timestamp);

  // Create continuous timeline by removing gaps
  const continuousData: FocusedOHLCVData[] = [];
  let timeOffset = 0;

  // Set gap parameters based on timeframe
  let normalInterval: number;
  let maxGap: number;

  switch (timeframe) {
    case '1min':
      normalInterval = 1 * 60 * 1000; // 1 minute
      maxGap = 2 * 60 * 60 * 1000; // 2 hours
      break;
    case '5min':
      normalInterval = 5 * 60 * 1000; // 5 minutes
      maxGap = 3 * 60 * 60 * 1000; // 3 hours
      break;
    case '15min':
      normalInterval = 15 * 60 * 1000; // 15 minutes
      maxGap = 4 * 60 * 60 * 1000; // 4 hours
      break;
    case '30min':
      normalInterval = 30 * 60 * 1000; // 30 minutes
      maxGap = 6 * 60 * 60 * 1000; // 6 hours
      break;
    case '1hour':
      normalInterval = 60 * 60 * 1000; // 1 hour
      maxGap = 12 * 60 * 60 * 1000; // 12 hours
      break;
    case '4hour':
      normalInterval = 4 * 60 * 60 * 1000; // 4 hours
      maxGap = 3 * 24 * 60 * 60 * 1000; // 3 days
      break;
    default:
      normalInterval = 15 * 60 * 1000; // Default to 15 minutes
      maxGap = 4 * 60 * 60 * 1000; // 4 hours
  }

  for (let i = 0; i < sortedData.length; i++) {
    const currentPoint = sortedData[i];

    if (i === 0) {
      // First point - no adjustment needed
      continuousData.push({
        ...currentPoint,
        timestamp: currentPoint.timestamp
      });
    } else {
      const previousPoint = sortedData[i - 1];
      const actualGap = currentPoint.timestamp - previousPoint.timestamp;

      if (actualGap > maxGap) {
        // Large gap detected (weekend, overnight, etc.) - compress it
        timeOffset += actualGap - normalInterval;
      }

      // Apply time offset to create continuous timeline
      continuousData.push({
        ...currentPoint,
        timestamp: currentPoint.timestamp - timeOffset,
        date: format(new Date(currentPoint.timestamp - timeOffset), 'yyyy-MM-dd HH:mm:ss')
      });
    }
  }

  return continuousData;
}

/**
 * Adjust trade timestamp to market open of the same day
 */
export function adjustTradeToMarketOpen(tradeTimestamp: number): number {
  const tradeDate = new Date(tradeTimestamp);
  const tradeDateOnly = tradeDate.toISOString().split('T')[0]; // Get YYYY-MM-DD

  // Set to 9:30 AM ET (14:30 UTC) on the same day
  const marketOpenTimestamp = new Date(tradeDateOnly + 'T14:30:00.000Z').getTime();

  return marketOpenTimestamp;
}

/**
 * Available candle intervals for trade analysis
 * Each represents how much time a single candle covers
 */
export type TradeTimeframe = '1min' | '5min' | '15min' | '30min' | '1hour' | '4hour' | 'daily';

/**
 * Get appropriate time ranges for different candle intervals
 * Larger timeframes need much wider date ranges to get enough candles for a full chart
 */
function getTimeframeContext(candleInterval: TradeTimeframe): { contextHours: number; extendedHours: number } {
  // Calculate time ranges to get approximately 50-100 candles for good chart visualization
  switch (candleInterval) {
    case '1min':
      return { contextHours: 4, extendedHours: 12 }; // 4 hours = ~240 one-minute candles
    case '5min':
      return { contextHours: 8, extendedHours: 24 }; // 8 hours = ~96 five-minute candles
    case '15min':
      return { contextHours: 16, extendedHours: 48 }; // 16 hours = ~64 fifteen-minute candles
    case '30min':
      return { contextHours: 32, extendedHours: 96 }; // 32 hours = ~64 thirty-minute candles
    case '1hour':
      return { contextHours: 72, extendedHours: 216 }; // 72 hours = ~72 one-hour candles
    case '4hour':
      return { contextHours: 480, extendedHours: 1440 }; // 480 hours (20 days) = ~120 four-hour candles
    case 'daily':
      return { contextHours: 1680, extendedHours: 5040 }; // 1680 hours (70 days) = ~70 daily candles
    default:
      return { contextHours: 16, extendedHours: 48 };
  }
}

/**
 * Fetch chart data for a specific trade with chosen candle interval
 * Each candle represents the specified time period (1min = 1-minute candles, 1hour = 1-hour candles, etc.)
 */
export async function fetchTradeChartData(
  symbol: string,
  tradeTimestamp: number,
  candleInterval: TradeTimeframe = '15min'
): Promise<FocusedOHLCVData[]> {
  try {
    // Calculate time range around the trade based on candle interval
    const tradeDate = new Date(tradeTimestamp);
    const { contextHours, extendedHours } = getTimeframeContext(candleInterval);

    // Calculate date range with appropriate context for the candle interval
    const startDate = format(subHours(tradeDate, extendedHours / 2), 'yyyy-MM-dd');
    const endDate = format(addHours(tradeDate, extendedHours / 2), 'yyyy-MM-dd');

    const requestBody = {
      action: 'focused-trade-data',
      symbol,
      startDate,
      endDate,
      timeframe: candleInterval // This tells the API what interval each candle should represent
    };

    // Call Supabase edge function
    const { data, error } = await supabase.functions.invoke('chart-processor', {
      body: JSON.stringify(requestBody)
    });

    if (error) {
      throw new Error(`Failed to fetch chart data for ${symbol}: ${error.message || 'Unknown error'}`);
    }

    if (!data) {
      throw new Error(`No response data from chart API for ${symbol}`);
    }

    if (!data.results) {
      throw new Error(`Invalid response format from chart API for ${symbol}`);
    }

    if (data.results.length === 0) {
      // Try with an even wider date range based on the timeframe
      let fallbackHours = extendedHours * 2; // Double the original range
      if (candleInterval === 'daily') {
        fallbackHours = 24 * 180; // 6 months for daily data
      } else if (candleInterval === '4hour') {
        fallbackHours = 24 * 60; // 2 months for 4-hour data
      }

      const widerStartDate = format(subHours(tradeDate, fallbackHours / 2), 'yyyy-MM-dd');
      const widerEndDate = format(addHours(tradeDate, fallbackHours / 2), 'yyyy-MM-dd');

      const widerRequestBody = {
        action: 'focused-trade-data',
        symbol,
        startDate: widerStartDate,
        endDate: widerEndDate,
        timeframe: candleInterval
      };

      const { data: widerData, error: widerError } = await supabase.functions.invoke('chart-processor', {
        body: JSON.stringify(widerRequestBody)
      });

      if (widerError || !widerData || !widerData.results || widerData.results.length === 0) {
        // Only try daily fallback if we're not already requesting daily data
        if (candleInterval !== 'daily') {
          const dailyRequestBody = {
            action: 'focused-trade-data',
            symbol,
            startDate: widerStartDate,
            endDate: widerEndDate,
            timeframe: 'daily'
          };

          const { data: dailyData, error: dailyError } = await supabase.functions.invoke('chart-processor', {
            body: JSON.stringify(dailyRequestBody)
          });

          if (dailyError || !dailyData || !dailyData.results || dailyData.results.length === 0) {
            throw new Error(`No chart data available for ${symbol} in any timeframe. This may be due to:\n• Market holidays or weekends\n• Invalid symbol\n• Data provider issues\n• Symbol not available in our data source\n\nPlease try a different symbol or check if the market was open during the trade period.`);
          }

          // Use daily data as fallback
          data.results = dailyData.results;
        } else {
          // We're already requesting daily data and got no results
          throw new Error(`No daily chart data available for ${symbol}. This may be due to:\n• Market holidays or weekends\n• Invalid symbol\n• Data provider issues\n• Symbol not available in our data source\n\nPlease try a different symbol or check if the market was open during the trade period.`);
        }
      } else {
        // We have wider data, use it
        data.results = widerData.results;
      }
    }

    // Transform API data to our format
    const rawData: FocusedOHLCVData[] = data.results.map((item: any) => ({
      timestamp: item.t,
      date: format(new Date(item.t), 'yyyy-MM-dd HH:mm:ss'),
      open: item.o,
      high: item.h,
      low: item.l,
      close: item.c,
      volume: item.v || 0
    }));

    // Remove gaps between trading sessions for continuous timeline
    const continuousData = removeTradingGaps(rawData, candleInterval);

    return continuousData;

  } catch (error) {
    throw new Error(`Failed to fetch trade chart data for ${symbol}: ${error.message || 'Unknown error'}`);
  }
}


