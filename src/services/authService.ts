import { supabase } from '@/integrations/supabase/client';
import { getCurrentAuthenticatedUser } from '@/utils/authUtils';

export interface AuthenticatedUser {
  id: string;
  email: string;
  user_metadata?: {
    username?: string;
    full_name?: string;
    avatar_url?: string;
    isWhopUser?: boolean;
    whop_user_id?: string;
    [key: string]: any;
  };
  isWhopUser: boolean;
}

/**
 * Universal authentication function that works for both regular and Whop users
 * This should be used by ALL services instead of supabase.auth.getUser()
 */
export const getAuthenticatedUser = async (): Promise<AuthenticatedUser> => {
  // First try regular Supabase authentication
  const { data: { user: supabaseUser }, error: supabaseError } = await supabase.auth.getUser();

  if (supabaseUser && !supabaseError) {
    return {
      id: supabaseUser.id,
      email: supabaseUser.email!,
      user_metadata: supabaseUser.user_metadata,
      isWhopUser: supabaseUser.user_metadata?.isWhopUser === true
    };
  }

  // If no Supabase user but we have Whop user data, try to re-authenticate
  if (typeof window !== 'undefined') {
    const whopUserData = localStorage.getItem('whop_user_data');
    if (whopUserData) {
      try {
        const whopUser = JSON.parse(whopUserData);

        const { createWhopSupabaseUser } = await import('./whopAuthService');
        const authResult = await createWhopSupabaseUser(whopUser);

        if (authResult.success && authResult.session) {

          // Get the user again after re-authentication
          const { data: { user: reAuthUser } } = await supabase.auth.getUser();
          if (reAuthUser) {
            return {
              id: reAuthUser.id,
              email: reAuthUser.email!,
              user_metadata: reAuthUser.user_metadata,
              isWhopUser: true
            };
          }
        } else {
          throw new Error('Whop user authentication failed - session required for API access');
        }
      } catch (reAuthError) {
        throw new Error('Whop user authentication failed - please refresh the page');
      }
    }
  }

  throw new Error('User not authenticated');
};

/**
 * Check if user is authenticated without throwing an error
 */
export const isAuthenticated = async (): Promise<boolean> => {
  try {
    await getAuthenticatedUser();
    return true;
  } catch {
    return false;
  }
};

/**
 * Get user ID for database operations
 */
export const getCurrentUserId = async (): Promise<string> => {
  const user = await getAuthenticatedUser();
  return user.id;
};

/**
 * Ensure user has a valid Supabase session, refreshing if necessary
 * This is useful for services that require authentication tokens
 */
export const ensureAuthenticated = async (): Promise<{ user: AuthenticatedUser; session: any }> => {
  // Get authenticated user (this will try to re-authenticate if needed)
  const user = await getAuthenticatedUser();

  // Get current session
  const { data: { session }, error } = await supabase.auth.getSession();

  if (!session?.access_token) {

    // For Whop users, provide specific guidance
    if (user.isWhopUser) {
      throw new Error('Whop user authentication session failed - please refresh the page');
    }

    throw new Error('Failed to establish authenticated session');
  }

  return { user, session };
};
