import { watchlistService } from './watchlistService';
import { fetchLatestPriceData } from './polygonService';

class WatchlistPriceService {
  private static instance: WatchlistPriceService;
  private updateInterval: NodeJS.Timeout | null = null;
  private isUpdating = false;

  static getInstance(): WatchlistPriceService {
    if (!WatchlistPriceService.instance) {
      WatchlistPriceService.instance = new WatchlistPriceService();
    }
    return WatchlistPriceService.instance;
  }

  // Start automatic price updates for watchlist items
  startPriceUpdates(intervalMs: number = 30000) { // Default 30 seconds
    if (this.updateInterval) {
      this.stopPriceUpdates();
    }

    // Update immediately
    this.updateAllWatchlistPrices();
    
    // Then update on interval
    this.updateInterval = setInterval(() => {
      this.updateAllWatchlistPrices();
    }, intervalMs);
  }

  // Stop automatic price updates
  stopPriceUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  // Update prices for all watchlist items
  async updateAllWatchlistPrices(): Promise<void> {
    if (this.isUpdating) {
      return;
    }

    try {
      this.isUpdating = true;

      // Get current watchlist
      const watchlist = await watchlistService.getWatchlist();

      if (watchlist.length === 0) {
        return;
      }

      // Update each symbol's price
      const updatePromises = watchlist.map(item => this.updateSymbolPrice(item.symbol));

      // Wait for all updates to complete
      await Promise.allSettled(updatePromises);

    } catch (error) {
      // console.error('📈 WatchlistPriceService: Error updating watchlist prices:', error);
    } finally {
      this.isUpdating = false;
    }
  }

  // Update price for a specific symbol
  async updateSymbolPrice(symbol: string): Promise<void> {
    try {
      // Fetch latest price data using 4H timeframe for better data availability
      const priceDataArray = await fetchLatestPriceData(symbol, '4H');

      if (!priceDataArray || priceDataArray.length === 0) {
        // Try with 1D timeframe as fallback
        const fallbackDataArray = await fetchLatestPriceData(symbol, '1D');
        if (!fallbackDataArray || fallbackDataArray.length === 0) {
          return;
        }

        // Use fallback data
        const fallbackData = fallbackDataArray[0];
        if (!fallbackData || !fallbackData.price || fallbackData.open === undefined) {
          return;
        }

        const currentPrice = fallbackData.price;
        const change = fallbackData.open ? currentPrice - fallbackData.open : 0;
        const changePercent = fallbackData.open ? (change / fallbackData.open) * 100 : 0;

        await watchlistService.updateStockPrice(symbol, currentPrice, change, changePercent);
        return;
      }

      // Get the first (and should be only) result for this symbol
      const priceData = priceDataArray[0];
      if (!priceData || priceData.price === undefined) {
        return;
      }

      // Get the latest data point
      const currentPrice = priceData.price;

      // Calculate change from 24 hours ago (6 periods of 4H = 24H)
      let change = 0;
      let changePercent = 0;

      if (priceDataArray.length >= 7) {
        // Use price from 6 periods ago (24 hours) for 24h change
        const price24hAgo = priceDataArray[priceDataArray.length - 7].price;
        change = currentPrice - price24hAgo;
        changePercent = (change / price24hAgo) * 100;
      } else if (priceDataArray.length >= 2) {
        // Fallback to previous period if we don't have 24h of data
        const previousClose = priceDataArray[priceDataArray.length - 2].price;
        change = currentPrice - previousClose;
        changePercent = (change / previousClose) * 100;
      } else if (priceData.open !== undefined) {
        // Final fallback to using open price
        change = currentPrice - priceData.open;
        changePercent = (change / priceData.open) * 100;
      }

      // Update the database
      await watchlistService.updateStockPrice(symbol, currentPrice, change, changePercent);

    } catch (error) {
      // console.error(`📈 WatchlistPriceService: Error updating price for ${symbol}:`, error);
    }
  }

  // Manual update for a single symbol (useful when adding to watchlist)
  async updateSingleSymbol(symbol: string): Promise<void> {
    await this.updateSymbolPrice(symbol);
  }

  // Check if price updates are running
  isRunning(): boolean {
    return this.updateInterval !== null;
  }
}

export const watchlistPriceService = WatchlistPriceService.getInstance();
