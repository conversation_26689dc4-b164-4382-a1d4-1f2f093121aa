/**
 * Example component showing how to use Vite environment variables
 * through the centralized configuration
 */

import React from 'react';
import { CONFIG, validateEnvironment } from '../../config/env';

export const EnvironmentExample: React.FC = () => {
  // Validate environment on component mount
  React.useEffect(() => {
    try {
      validateEnvironment();
    } catch (error) {
      console.error('Environment validation failed:', error);
    }
  }, []);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Environment Configuration Example</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Supabase Configuration */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Supabase Configuration</h2>
          <div className="space-y-2 text-sm">
            <div>
              <strong>URL:</strong> {CONFIG.supabase.url || 'Not set'}
            </div>
            <div>
              <strong>Anon Key:</strong> {CONFIG.supabase.anonKey ? `${CONFIG.supabase.anonKey.substring(0, 20)}...` : 'Not set'}
            </div>
          </div>
        </div>

        {/* Whop Configuration */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Whop Configuration</h2>
          <div className="space-y-2 text-sm">
            <div>
              <strong>Intermediary URL:</strong> {CONFIG.whop.intermediaryUrl || 'Not set'}
            </div>
            <div>
              <strong>App ID:</strong> {CONFIG.whop.appId || 'Not set'}
            </div>
            <div>
              <strong>Trading App ID:</strong> {CONFIG.whop.tradingAppId || 'Not set'}
            </div>
          </div>
        </div>

        {/* Stripe Configuration */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Stripe Configuration</h2>
          <div className="space-y-2 text-sm">
            <div>
              <strong>Publishable Key:</strong> {CONFIG.stripe.publishableKey ? `${CONFIG.stripe.publishableKey.substring(0, 20)}...` : 'Not set'}
            </div>
            <div>
              <strong>Live Price ID:</strong> {CONFIG.stripe.livePriceId || 'Not set'}
            </div>
          </div>
        </div>

        {/* API Keys */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">API Keys</h2>
          <div className="space-y-2 text-sm">
            <div>
              <strong>Polygon:</strong> {CONFIG.apiKeys.polygon ? `${CONFIG.apiKeys.polygon.substring(0, 10)}...` : 'Not set'}
            </div>
            <div>
              <strong>Gemini:</strong> {CONFIG.apiKeys.gemini ? `${CONFIG.apiKeys.gemini.substring(0, 10)}...` : 'Not set'}
            </div>
            <div>
              <strong>Alpha Vantage:</strong> {CONFIG.apiKeys.alphaVantage ? `${CONFIG.apiKeys.alphaVantage.substring(0, 10)}...` : 'Not set'}
            </div>
          </div>
        </div>

        {/* PostHog Configuration */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">PostHog Configuration</h2>
          <div className="space-y-2 text-sm">
            <div>
              <strong>Key:</strong> {CONFIG.posthog.key ? `${CONFIG.posthog.key.substring(0, 20)}...` : 'Not set'}
            </div>
            <div>
              <strong>Host:</strong> {CONFIG.posthog.host || 'Not set'}
            </div>
          </div>
        </div>

        {/* Environment Info */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Environment Info</h2>
          <div className="space-y-2 text-sm">
            <div>
              <strong>Mode:</strong> {CONFIG.env.mode}
            </div>
            <div>
              <strong>Development:</strong> {CONFIG.env.isDevelopment ? 'Yes' : 'No'}
            </div>
            <div>
              <strong>Production:</strong> {CONFIG.env.isProduction ? 'Yes' : 'No'}
            </div>
          </div>
        </div>
      </div>

      {/* Usage Examples */}
      <div className="mt-8">
        <h2 className="text-lg font-semibold mb-4">Usage Examples</h2>
        
        <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
          <pre className="text-sm">
{`// Import the centralized configuration
import { CONFIG, API_KEYS, SUPABASE_CONFIG } from '../config/env';

// Use Supabase configuration
const supabase = createClient(CONFIG.supabase.url, CONFIG.supabase.anonKey);

// Use API keys
const polygonApiKey = CONFIG.apiKeys.polygon;
const geminiApiKey = API_KEYS.gemini;

// Use Whop configuration
const whopUrl = CONFIG.whop.intermediaryUrl;

// Check environment
if (CONFIG.env.isDevelopment) {
  // Console logging removed
}

// Use individual configurations
const stripeKey = STRIPE_CONFIG.publishableKey;`}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default EnvironmentExample;
