import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  User, 
  Crown, 
  Shield, 
  Building, 
  Mail, 
  Key, 
  Eye, 
  EyeOff,
  RefreshCw,
  Copy,
  Check
} from 'lucide-react';
import { gatherUserInfo, printUserInfo, UserInfoData } from '@/utils/userInfoUtils';
import { useToast } from '@/components/ui/use-toast';

const UserInfoDisplay: React.FC = () => {
  const [userInfo, setUserInfo] = useState<UserInfoData | null>(null);
  const [loading, setLoading] = useState(true);
  const [showSensitive, setShowSensitive] = useState(false);
  const [copiedField, setCopiedField] = useState<string | null>(null);
  const { toast } = useToast();

  const loadUserInfo = async () => {
    setLoading(true);
    try {
      const info = await gatherUserInfo();
      setUserInfo(info);
    } catch (error) {
      console.error('Error loading user info:', error);
      toast({
        title: "Error",
        description: "Failed to load user information",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUserInfo();
  }, []);

  const handlePrintToConsole = async () => {
    await printUserInfo();
    toast({
      title: "User Info Printed",
      description: "Check the browser console for detailed information",
    });
  };

  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      setTimeout(() => setCopiedField(null), 2000);
      toast({
        title: "Copied",
        description: `${fieldName} copied to clipboard`,
      });
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const CopyableField: React.FC<{ 
    label: string; 
    value: string | null; 
    sensitive?: boolean;
    fieldKey: string;
  }> = ({ label, value, sensitive = false, fieldKey }) => {
    const displayValue = value || 'N/A';
    const shouldHide = sensitive && !showSensitive && value;
    const maskedValue = shouldHide ? '••••••••••••••••' : displayValue;

    return (
      <div className="flex items-center justify-between py-1">
        <span className="text-sm text-white/60">{label}:</span>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-white font-mono">
            {maskedValue}
          </span>
          {value && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => copyToClipboard(value, label)}
            >
              {copiedField === label ? (
                <Check className="h-3 w-3 text-green-400" />
              ) : (
                <Copy className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
      </div>
    );
  };

  const StatusBadge: React.FC<{ 
    condition: boolean; 
    trueText: string; 
    falseText: string;
    trueColor?: string;
    falseColor?: string;
  }> = ({ 
    condition, 
    trueText, 
    falseText, 
    trueColor = "bg-green-500/20 text-green-400 border-green-500/30",
    falseColor = "bg-red-500/20 text-red-400 border-red-500/30"
  }) => (
    <Badge className={condition ? trueColor : falseColor}>
      {condition ? trueText : falseText}
    </Badge>
  );

  if (loading) {
    return (
      <Card className="bg-white/[0.02] border-white/[0.06]">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
            <span className="ml-2 text-white/60">Loading user information...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!userInfo) {
    return (
      <Card className="bg-white/[0.02] border-white/[0.06]">
        <CardContent className="p-6">
          <div className="text-center text-white/60">
            Failed to load user information
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with actions */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-white">User Information</h2>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSensitive(!showSensitive)}
            className="border-white/20 text-white/80 hover:bg-white/10"
          >
            {showSensitive ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            <span className="ml-2">{showSensitive ? 'Hide' : 'Show'} Sensitive</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={loadUserInfo}
            className="border-white/20 text-white/80 hover:bg-white/10"
          >
            <RefreshCw className="h-4 w-4" />
            <span className="ml-2">Refresh</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrintToConsole}
            className="border-white/20 text-white/80 hover:bg-white/10"
          >
            Print to Console
          </Button>
        </div>
      </div>

      {/* Regular User Info */}
      <Card className="bg-white/[0.02] border-white/[0.06]">
        <CardHeader>
          <CardTitle className="flex items-center text-white">
            <User className="h-5 w-5 mr-2" />
            Regular User Info
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {userInfo.user ? (
            <>
              <CopyableField 
                label="User ID" 
                value={userInfo.user.id} 
                sensitive={true}
                fieldKey="userId"
              />
              <CopyableField 
                label="Email" 
                value={userInfo.user.email} 
                fieldKey="email"
              />
              <div className="flex items-center justify-between py-1">
                <span className="text-sm text-white/60">Is Whop User:</span>
                <StatusBadge 
                  condition={userInfo.user.isWhopUser}
                  trueText="Yes"
                  falseText="No"
                />
              </div>
            </>
          ) : (
            <div className="text-center text-white/60 py-4">
              No user found
            </div>
          )}
        </CardContent>
      </Card>

      {/* Whop User Info */}
      <Card className="bg-white/[0.02] border-white/[0.06]">
        <CardHeader>
          <CardTitle className="flex items-center text-white">
            <Building className="h-5 w-5 mr-2" />
            Whop User Info
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {userInfo.whopUser ? (
            <>
              <CopyableField
                label="Whop ID"
                value={userInfo.whopUser.id}
                sensitive={true}
                fieldKey="whopId"
              />
              <CopyableField
                label="Username"
                value={userInfo.whopUser.username}
                fieldKey="username"
              />
              <CopyableField
                label="Email"
                value={userInfo.whopUser.email}
                fieldKey="whopEmail"
              />
              <CopyableField
                label="Profile Pic URL"
                value={userInfo.whopUser.profilePicUrl}
                fieldKey="profilePic"
              />
            </>
          ) : (
            <div className="text-center text-white/60 py-4">
              Not a Whop user
            </div>
          )}
        </CardContent>
      </Card>

      {/* Whop Owner Info */}
      <Card className="bg-white/[0.02] border-white/[0.06]">
        <CardHeader>
          <CardTitle className="flex items-center text-white">
            <Crown className="h-5 w-5 mr-2 text-yellow-400" />
            Whop Owner Info
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between py-1">
            <span className="text-sm text-white/60">Is Whop Owner:</span>
            <StatusBadge
              condition={userInfo.whopOwnerInfo.isWhopOwner}
              trueText="YES - Owner/Admin"
              falseText="No"
              trueColor="bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
            />
          </div>

          <CopyableField
            label="Access Level"
            value={userInfo.whopOwnerInfo.accessLevel}
            fieldKey="accessLevel"
          />

          <CopyableField
            label="Company ID"
            value={userInfo.whopOwnerInfo.companyId}
            sensitive={true}
            fieldKey="companyId"
          />

          <CopyableField
            label="Business ID"
            value={userInfo.whopOwnerInfo.businessId}
            sensitive={true}
            fieldKey="businessId"
          />

          <CopyableField
            label="Business Handle"
            value={userInfo.whopOwnerInfo.businessHandle}
            fieldKey="businessHandle"
          />

          <div className="flex items-center justify-between py-1">
            <span className="text-sm text-white/60">Is Official Osis:</span>
            <StatusBadge
              condition={userInfo.whopOwnerInfo.isOfficialOsis}
              trueText="YES - Official"
              falseText="No"
              trueColor="bg-blue-500/20 text-blue-400 border-blue-500/30"
            />
          </div>
        </CardContent>
      </Card>

      {/* Admin Info */}
      <Card className="bg-white/[0.02] border-white/[0.06]">
        <CardHeader>
          <CardTitle className="flex items-center text-white">
            <Shield className="h-5 w-5 mr-2 text-red-400" />
            Admin Info
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between py-1">
            <span className="text-sm text-white/60">Is Admin:</span>
            <StatusBadge
              condition={userInfo.adminInfo.isAdmin}
              trueText="YES - App Admin"
              falseText="No"
              trueColor="bg-red-500/20 text-red-400 border-red-500/30"
            />
          </div>

          <div className="py-1">
            <span className="text-sm text-white/60">Admin Emails:</span>
            <div className="mt-1 space-y-1">
              {userInfo.adminInfo.adminEmails.map((email, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-white font-mono">{email}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => copyToClipboard(email, `Admin Email ${index + 1}`)}
                  >
                    {copiedField === `Admin Email ${index + 1}` ? (
                      <Check className="h-3 w-3 text-green-400" />
                    ) : (
                      <Copy className="h-3 w-3" />
                    )}
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Access Info */}
      <Card className="bg-white/[0.02] border-white/[0.06]">
        <CardHeader>
          <CardTitle className="flex items-center text-white">
            <Key className="h-5 w-5 mr-2 text-green-400" />
            Access Info
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between py-1">
            <span className="text-sm text-white/60">Has Access:</span>
            <StatusBadge
              condition={userInfo.accessInfo.hasAccess}
              trueText="Access Granted"
              falseText="No Access"
              trueColor="bg-green-500/20 text-green-400 border-green-500/30"
            />
          </div>

          <CopyableField
            label="Access Level"
            value={userInfo.accessInfo.accessLevel}
            fieldKey="userAccessLevel"
          />

          <CopyableField
            label="Experience ID"
            value={userInfo.accessInfo.experienceId}
            sensitive={true}
            fieldKey="experienceId"
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default UserInfoDisplay;
