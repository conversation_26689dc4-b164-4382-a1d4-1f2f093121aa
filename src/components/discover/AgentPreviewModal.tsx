import React, { useState, useEffect } from 'react';
import { X, Star, Download, User, Calendar, Tag, MessageSquare, ThumbsUp, ThumbsDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { formatDistanceToNow } from 'date-fns';
import { type PublishedAgent, type AgentReview, getAgentReviews, createAgentReview } from '@/services/discoverService';

interface AgentPreviewModalProps {
  agent: PublishedAgent;
  isOpen: boolean;
  onClose: () => void;
  onImport: () => void;
  importing: boolean;
}

const AgentPreviewModal: React.FC<AgentPreviewModalProps> = ({
  agent,
  isOpen,
  onClose,
  onImport,
  importing
}) => {
  const { toast } = useToast();
  const [reviews, setReviews] = useState<AgentReview[]>([]);
  const [loadingReviews, setLoadingReviews] = useState(false);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [reviewForm, setReviewForm] = useState({
    rating: 5,
    reviewText: ''
  });
  const [submittingReview, setSubmittingReview] = useState(false);

  // Load reviews when modal opens
  useEffect(() => {
    if (isOpen) {
      loadReviews();
    }
  }, [isOpen, agent.id]);

  const loadReviews = async () => {
    try {
      setLoadingReviews(true);
      const response = await getAgentReviews(agent.id);
      if (response.success) {
        setReviews(response.reviews);
      }
    } catch (error) {
      console.error('Error loading reviews:', error);
    } finally {
      setLoadingReviews(false);
    }
  };

  const handleSubmitReview = async () => {
    try {
      setSubmittingReview(true);
      
      const response = await createAgentReview({
        publishedAgentId: agent.id,
        rating: reviewForm.rating,
        reviewText: reviewForm.reviewText
      });

      if (response.success) {
        toast({
          title: "Success",
          description: "Review submitted successfully"
        });
        setShowReviewForm(false);
        setReviewForm({ rating: 5, reviewText: '' });
        loadReviews(); // Refresh reviews
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: response.error || "Failed to submit review"
        });
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred"
      });
    } finally {
      setSubmittingReview(false);
    }
  };

  const renderStars = (rating: number, interactive = false, onRatingChange?: (rating: number) => void) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-5 h-5 ${
          i < Math.floor(rating)
            ? 'text-yellow-400 fill-current'
            : i < rating
            ? 'text-yellow-400 fill-current opacity-50'
            : 'text-gray-600'
        } ${interactive ? 'cursor-pointer hover:text-yellow-300' : ''}`}
        onClick={interactive && onRatingChange ? () => onRatingChange(i + 1) : undefined}
      />
    ));
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'Day Trading': 'bg-red-500/20 text-red-300 border-red-500/30',
      'Technical Analysis': 'bg-blue-500/20 text-blue-300 border-blue-500/30',
      'Options Trading': 'bg-purple-500/20 text-purple-300 border-purple-500/30',
      'Swing Trading': 'bg-green-500/20 text-green-300 border-green-500/30',
      'Risk Management': 'bg-orange-500/20 text-orange-300 border-orange-500/30',
      'Market Scanning': 'bg-cyan-500/20 text-cyan-300 border-cyan-500/30',
      'Fundamental Analysis': 'bg-indigo-500/20 text-indigo-300 border-indigo-500/30',
      'General': 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    };
    return colors[category] || colors['General'];
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] bg-gray-900 border-gray-700 text-white overflow-y-auto">
        <DialogHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <DialogTitle className="text-2xl font-bold">{agent.name}</DialogTitle>
                {agent.isFeatured && (
                  <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white">
                    Featured
                  </Badge>
                )}
              </div>
              <DialogDescription className="sr-only">
                Preview and import agent {agent.name} by {agent.publisherName}. View configuration, reviews, and download details.
              </DialogDescription>
              <div className="flex items-center gap-4 text-sm text-gray-400">
                <div className="flex items-center gap-1">
                  <User className="w-4 h-4" />
                  <span>{agent.publisherName}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDistanceToNow(new Date(agent.createdAt), { addSuffix: true })}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Download className="w-4 h-4" />
                  <span>{agent.downloadCount} downloads</span>
                </div>
              </div>
            </div>
            <Button
              onClick={onImport}
              disabled={importing}
              className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
            >
              {importing ? 'Importing...' : 'Import to Library'}
            </Button>
          </div>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-[#0A0A0A] border border-[#1F2937]/40 rounded-lg p-1">
            <TabsTrigger
              value="overview"
              className="data-[state=active]:bg-[#141414] data-[state=active]:text-white data-[state=active]:shadow-sm text-white/60 hover:text-white/80 transition-all duration-200 rounded-md font-medium"
            >
              Overview
            </TabsTrigger>
            <TabsTrigger
              value="configuration"
              className="data-[state=active]:bg-[#141414] data-[state=active]:text-white data-[state=active]:shadow-sm text-white/60 hover:text-white/80 transition-all duration-200 rounded-md font-medium"
            >
              Configuration
            </TabsTrigger>
            <TabsTrigger
              value="reviews"
              className="data-[state=active]:bg-[#141414] data-[state=active]:text-white data-[state=active]:shadow-sm text-white/60 hover:text-white/80 transition-all duration-200 rounded-md font-medium"
            >
              Reviews ({reviews.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Basic Info */}
            <Card className="bg-[#141414]/60 border border-[#1F2937]/40 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)]">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Badge className={`border ${getCategoryColor(agent.category)}`}>
                      {agent.category}
                    </Badge>
                    <div className="flex items-center gap-1">
                      {renderStars(agent.averageRating)}
                      <span className="text-sm text-white/60 ml-2">
                        {agent.averageRating.toFixed(1)} ({agent.totalReviews} reviews)
                      </span>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-white/80 text-base leading-relaxed">
                  {agent.description || 'No description available for this agent.'}
                </CardDescription>
              </CardContent>
            </Card>

            {/* Tags */}
            {agent.tags.length > 0 && (
              <Card className="bg-[#141414]/60 border border-[#1F2937]/40 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)]">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2 text-white">
                    <Tag className="w-5 h-5" />
                    Tags
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {agent.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="border-[#1F2937] text-white/70 bg-[#141414]/40">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="configuration" className="space-y-6">
            <Card className="bg-[#141414]/60 border border-[#1F2937]/40 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)]">
              <CardHeader>
                <CardTitle className="text-lg text-white">Agent Configuration</CardTitle>
                <CardDescription className="text-white/60">
                  Technical details about how this agent is configured
                </CardDescription>
              </CardHeader>
              <CardContent>
                {agent.configuration ? (
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium text-white/80 mb-2">Blocks</h4>
                      <p className="text-sm text-white/60">
                        {agent.configuration.blocks?.length || 0} blocks configured
                      </p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-white/80 mb-2">Entry Point</h4>
                      <p className="text-sm text-white/60">
                        {agent.configuration.entryBlockId || 'Not specified'}
                      </p>
                    </div>
                    {/* Add more configuration details as needed */}
                  </div>
                ) : (
                  <p className="text-white/60">Configuration details not available</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reviews" className="space-y-6">
            {/* Add Review Button */}
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-white">User Reviews</h3>
              <Button
                onClick={() => setShowReviewForm(!showReviewForm)}
                variant="outline"
                className="border-[#1F2937] text-white/70 hover:bg-[#141414] hover:text-white transition-colors"
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Write Review
              </Button>
            </div>

            {/* Review Form */}
            {showReviewForm && (
              <Card className="bg-[#141414]/60 border border-[#1F2937]/40 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)]">
                <CardHeader>
                  <CardTitle className="text-lg text-white">Write a Review</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-white/80 mb-2 block">Rating</label>
                    <div className="flex items-center gap-1">
                      {renderStars(reviewForm.rating, true, (rating) =>
                        setReviewForm(prev => ({ ...prev, rating }))
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-white/80 mb-2 block">Review (Optional)</label>
                    <Textarea
                      value={reviewForm.reviewText}
                      onChange={(e) => setReviewForm(prev => ({ ...prev, reviewText: e.target.value }))}
                      placeholder="Share your experience with this agent..."
                      rows={4}
                      className="bg-[#141414] border-[#1F2937] text-white placeholder:text-white/40"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={handleSubmitReview}
                      disabled={submittingReview}
                      className="bg-[#00e7b6] hover:bg-[#00d4a3] text-black font-medium"
                    >
                      {submittingReview ? 'Submitting...' : 'Submit Review'}
                    </Button>
                    <Button
                      onClick={() => setShowReviewForm(false)}
                      variant="outline"
                      className="border-[#1F2937] text-white/70 hover:bg-[#141414] hover:text-white"
                    >
                      Cancel
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Reviews List */}
            {loadingReviews ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00e7b6] mx-auto mb-4"></div>
                <p className="text-white/60">Loading reviews...</p>
              </div>
            ) : reviews.length === 0 ? (
              <Card className="bg-[#141414]/40 border border-[#1F2937]/20 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)]">
                <CardContent className="text-center py-12">
                  <MessageSquare className="w-16 h-16 text-white/20 mx-auto mb-6" />
                  <h4 className="text-lg font-medium text-white mb-2">No reviews yet</h4>
                  <p className="text-white/60 mb-6">Be the first to share your experience with this agent</p>
                  <Button
                    onClick={() => setShowReviewForm(true)}
                    className="bg-[#00e7b6] hover:bg-[#00d4a3] text-black font-medium"
                  >
                    <MessageSquare className="w-4 h-4 mr-2" />
                    Write First Review
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {reviews.map((review) => (
                  <Card key={review.id} className="bg-[#141414]/60 border border-[#1F2937]/40 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] hover:border-[#1F2937]/60 transition-colors">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-[#00e7b6] to-[#00d4a3] rounded-full flex items-center justify-center text-black text-sm font-bold">
                            {review.reviewerName.charAt(0).toUpperCase()}
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-white">{review.reviewerName}</span>
                              {review.isVerified && (
                                <Badge variant="outline" className="border-[#00e7b6] text-[#00e7b6] bg-[#00e7b6]/10 text-xs">
                                  Verified
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center gap-2 mt-1">
                              <div className="flex items-center gap-1">
                                {renderStars(review.rating)}
                              </div>
                              <span className="text-xs text-white/50">{formatDistanceToNow(new Date(review.createdAt), { addSuffix: true })}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      {review.reviewText && (
                        <p className="text-white/80 leading-relaxed">{review.reviewText}</p>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default AgentPreviewModal;
