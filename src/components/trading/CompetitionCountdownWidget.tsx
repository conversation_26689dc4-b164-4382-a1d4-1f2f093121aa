import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

interface CompetitionCountdownWidgetProps {
  isVisible: boolean;
}

const CompetitionCountdownWidget: React.FC<CompetitionCountdownWidgetProps> = ({ isVisible }) => {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining>({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  
  // Set competition end date - August 25th, 2025 at 11:59 PM EST (2 weeks after start)
  const competitionEndDate = new Date('2025-08-26T03:59:59Z'); // 11:59 PM EDT = 03:59 UTC next day
  
  const calculateTimeRemaining = (targetDate: Date): TimeRemaining => {
    const now = new Date().getTime();
    const target = targetDate.getTime();
    const difference = target - now;

    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }

    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    return { days, hours, minutes, seconds };
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setTimeRemaining(calculateTimeRemaining(competitionEndDate));
    }, 1000);

    return () => clearInterval(interval);
  }, [competitionEndDate]);

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="flex flex-col sm:flex-row items-center gap-2 sm:gap-4"
    >
      {/* Logo */}
      <div className="flex items-center gap-3">
        <img
          src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/logos/ChatGPT%20Image%20Jul%2010,%202025,%2006_30_31%20PM.png"
          alt="TradeOff Logo"
          className="w-6 h-6 sm:w-8 sm:h-8 object-contain"
        />
        <div className="text-white/60 text-xs sm:text-sm font-medium text-center sm:text-left">
          Competition ends in:
        </div>
      </div>

      {/* Countdown */}
      <div className="flex items-center gap-1 sm:gap-2">
        {/* Days */}
        {timeRemaining.days > 0 && (
          <>
            <div className="bg-[#141414] border border-white/10 rounded-lg px-2 sm:px-3 py-1 sm:py-1.5 min-w-[2.5rem] sm:min-w-[3rem] text-center">
              <div 
                className="text-white font-mono text-lg font-bold leading-none"
                style={{ letterSpacing: '-9px' }}
              >
                {timeRemaining.days.toString().padStart(2, '0')}
              </div>
              <div className="text-white/50 text-xs font-medium mt-0.5">DAYS</div>
            </div>
            <div className="text-white/30 text-lg font-bold">:</div>
          </>
        )}

        {/* Hours */}
        <div className="bg-[#141414] border border-white/10 rounded-lg px-3 py-1.5 min-w-[3rem] text-center">
          <div 
            className="text-white font-mono text-lg font-bold leading-none"
            style={{ letterSpacing: '-9px' }}
          >
            {timeRemaining.hours.toString().padStart(2, '0')}
          </div>
          <div className="text-white/50 text-xs font-medium mt-0.5">HRS</div>
        </div>
        <div className="text-white/30 text-lg font-bold">:</div>

        {/* Minutes */}
        <div className="bg-[#141414] border border-white/10 rounded-lg px-3 py-1.5 min-w-[3rem] text-center">
          <div 
            className="text-white font-mono text-lg font-bold leading-none"
            style={{ letterSpacing: '-9px' }}
          >
            {timeRemaining.minutes.toString().padStart(2, '0')}
          </div>
          <div className="text-white/50 text-xs font-medium mt-0.5">MIN</div>
        </div>
        <div className="text-white/30 text-lg font-bold">:</div>

        {/* Seconds */}
        <div className="bg-[#141414] border border-white/10 rounded-lg px-3 py-1.5 min-w-[3rem] text-center">
          <motion.div 
            key={timeRemaining.seconds}
            initial={{ scale: 1.1 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.2 }}
            className="text-white font-mono text-lg font-bold leading-none"
            style={{ letterSpacing: '-9px' }}
          >
            {timeRemaining.seconds.toString().padStart(2, '0')}
          </motion.div>
          <div className="text-white/50 text-xs font-medium mt-0.5">SEC</div>
        </div>
      </div>
    </motion.div>
  );
};

export default CompetitionCountdownWidget;
