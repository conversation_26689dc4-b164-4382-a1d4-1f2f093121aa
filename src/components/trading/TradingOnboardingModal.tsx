import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
// Removed Dialog imports - using direct div instead for debugging
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  TrendingUp,
  BarChart3,
  Trophy
} from 'lucide-react';
import { useIframeSdk } from '@/hooks/useIframeSdk';
import { whopIntermediaryClient } from '@/lib/whopIntermediaryClient';
import { useToast } from '@/components/ui/use-toast';
import { useWhopAccess } from '@/contexts/WhopContext';

interface TradingOnboardingModalProps {
  isOpen: boolean;
  onComplete: () => void;
  onPreview?: () => void;
}

const TradingOnboardingModal: React.FC<TradingOnboardingModalProps> = ({
  isOpen,
  onComplete,
  onPreview
}) => {
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [timeLeft, setTimeLeft] = useState({ days: 0, hours: 0, minutes: 0, seconds: 0 });

  const iframeSdk = useIframeSdk();
  const { toast } = useToast();
  const { isAdmin, accessLevel } = useWhopAccess();

  // Enhanced mobile-friendly owner detection
  const isWhopOwner = React.useMemo(() => {
    // Primary check: useWhopAccess hook
    if (isAdmin || accessLevel === 'admin') {
      return true;
    }


    return false;
  }, [isAdmin, accessLevel]);

  // Debug for owner detection
  React.useEffect(() => {
    console.group('🎯 Trading Onboarding Modal - Whop Access Debug');
    console.log('🔑 Whop Access Info:', {
      isAdmin,
      accessLevel,
      isWhopOwner,
      calculation: `${isAdmin} || ${accessLevel} === 'admin' = ${isWhopOwner}`,
      timestamp: new Date().toISOString()
    });
    console.groupEnd();
  }, [isAdmin, accessLevel, isWhopOwner]);

  // Save admin status when detected for mobile persistence
  React.useEffect(() => {
    if (isAdmin || accessLevel === 'admin') {
      localStorage.setItem('whop_access_level', 'admin');
      console.log('💾 Saved admin status for mobile persistence');
    }
  }, [isAdmin, accessLevel]);

  // Countdown timer logic - August 11th, 2025
  useEffect(() => {
    const targetDate = new Date('2025-08-11T23:59:59').getTime();

    const updateCountdown = () => {
      const now = new Date().getTime();
      const distance = targetDate - now;

      if (distance > 0) {
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        setTimeLeft({ days, hours, minutes, seconds });
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
      }
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, []);





  const handlePayment = async () => {
    // Skip payment for Whop owners
    if (isWhopOwner) {
      console.log('👑 Whop owner detected, skipping payment and completing onboarding');
      toast({
        title: "Welcome, Community Owner!",
        description: "You have free access to the trading competition. Get ready to trade!",
      });
      onComplete();
      return;
    }

    if (!iframeSdk) {
      setPaymentError('Payment system not available');
      return;
    }

    setIsProcessingPayment(true);
    setPaymentError(null);

    try {
      console.log('💳 Starting Whop payment flow for trading competition...');

      // Get experience ID for affiliate tracking
      const experienceId = new URLSearchParams(window.location.search).get('experienceId') ||
                          window.location.pathname.split('/experiences/')[1];

      // Create charge via intermediary server with affiliate metadata
      const chargeResponse = await whopIntermediaryClient.createCharge(
        10, // $10.00 - competition entry fee (in dollars, not cents)
        'usd',
        'TradeOff Trading Competition Entry',
        {
          experienceId,
          affiliatePayoutAmount: 1, // $1 affiliate payout
          isAffiliateEligible: true
        }
      );

      console.log('📡 Charge creation response:', chargeResponse);

      if (!chargeResponse.success || !chargeResponse.data?.inAppPurchase) {
        throw new Error(chargeResponse.error || 'Failed to create charge');
      }

      // Open Whop payment modal
      console.log('🖼️ Opening Whop payment modal...');
      const paymentResult = await iframeSdk.inAppPurchase(chargeResponse.data.inAppPurchase);

      console.log('💳 Payment result:', paymentResult);

      if (paymentResult?.status === "ok") {
        console.log('✅ Payment successful, processing affiliate payout...');

        // Process affiliate payout if we have the necessary data
        if (experienceId && paymentResult.data?.receiptId) {
          try {
            await processAffiliatePayout(experienceId, paymentResult.data.receiptId, paymentResult.data.sessionId);
          } catch (affiliateError) {
            console.error('⚠️ Affiliate payout failed, but payment was successful:', affiliateError);
            // Don't fail the main flow if affiliate payout fails
          }
        }

        toast({
          title: "Welcome to the Trading Competition!",
          description: "Your entry has been confirmed. Get ready to trade!",
        });
        onComplete();
      } else {
        throw new Error(paymentResult?.error || 'Payment was not completed');
      }
    } catch (error) {
      console.error('❌ Payment error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Payment failed';
      setPaymentError(errorMessage);
      toast({
        title: "Payment Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const processAffiliatePayout = async (experienceId: string, receiptId: string, userId: string) => {
    try {
      console.log('💰 Processing affiliate payout...', { experienceId, receiptId, userId });

      // Get the community owner's username from the experience
      // This would typically be done server-side, but for now we'll use a placeholder
      const ownerUsername = 'community_owner'; // This should be fetched from the experience data

      const payoutResponse = await whopIntermediaryClient.sendAffiliatePayout(
        ownerUsername,
        1, // $1 affiliate payout
        experienceId
      );

      if (payoutResponse.success) {
        console.log('✅ Affiliate payout sent successfully:', payoutResponse);
      } else {
        throw new Error(payoutResponse.error || 'Failed to send affiliate payout');
      }
    } catch (error) {
      console.error('❌ Affiliate payout failed:', error);
      throw error;
    }
  };

  // Handle owner free access
  const handleOwnerAccess = () => {
    console.log('👑 Whop owner detected, providing free access');
    toast({
      title: "Welcome, Community Owner!",
      description: "You have free access to the trading competition. Get ready to trade!",
    });
    onComplete();
  };

  // Handle "Join Now" button click
  const handleJoinNow = () => {
    if (isWhopOwner) {
      // Free access for owners
      handleOwnerAccess();
    } else {
      // $10 payment for regular users
      handlePayment();
    }
  };

  // Handle "Not Now" button click
  const handleNotNow = () => {
    onPreview?.(); // This will dismiss the modal and show the chart
  };

  const renderCombinedCard = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="space-y-6"
    >
      {/* Header Section with Logo and Typography */}
      <div className="text-center space-y-4">
        <div className="flex justify-center mb-4">
          <div className="relative">
            <img
              src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/logos/ChatGPT%20Image%20Jul%2010,%202025,%2006_41_45%20PM.png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJsb2dvcy9DaGF0R1BUIEltYWdlIEp1bCAxMCwgMjAyNSwgMDZfNDFfNDUgUE0ucG5nIiwiaWF0IjoxNzUzNTA1MDI4LCJleHAiOjE3ODUwNDEwMjh9.JHAAqCN14J2Qt0-rPdvvOvPMrX4VnMHTfr3FKXIFWjM"
              alt="TradeOff Logo"
              className="w-20 h-20 object-contain filter drop-shadow-lg"
            />
            {/* Subtle glow effect around logo */}
            <div className="absolute inset-0 w-20 h-20 bg-gradient-to-br from-white/5 to-transparent rounded-full blur-xl" />
          </div>
        </div>

        {/* Main Title with Gold Accent and Detailed Typography */}
        <div className="space-y-3">
          <h1
            className="text-3xl sm:text-4xl font-semibold bg-gradient-to-br from-gray-100 via-gray-200 to-gray-400 bg-clip-text text-transparent leading-tight"
            style={{
              fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif',
              letterSpacing: '-0.03em',
              textShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}
          >
            Join Trading Competition
          </h1>

          {/* Subtitle with refined gradient and negative spacing */}
          <p
            className="text-lg bg-gradient-to-r from-gray-300 via-gray-400 to-gray-500 bg-clip-text text-transparent leading-relaxed"
            style={{
              fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
              letterSpacing: '-0.01em'
            }}
          >
            Real-time paper trading with $100,000 starting balance
          </p>
        </div>
      </div>

      {/* Features Section with Clean Typography */}
      <div className="space-y-3">
        <h3
          className="text-sm font-medium bg-gradient-to-r from-gray-300 to-gray-400 bg-clip-text text-transparent uppercase"
          style={{
            fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
            letterSpacing: '0.05em'
          }}
        >
          What's Included
        </h3>

        <div className="grid grid-cols-1 gap-3">
          <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-white/[0.02] to-white/[0.01] rounded-xl border border-white/[0.08] shadow-[inset_0_1px_0_rgba(255,255,255,0.02),0_2px_8px_rgba(0,0,0,0.04)]">
            <div className="w-10 h-10 bg-gradient-to-br from-white/[0.08] to-white/[0.03] border border-white/[0.12] rounded-lg flex items-center justify-center shadow-[inset_0_1px_0_rgba(255,255,255,0.05)]">
              <TrendingUp className="w-5 h-5 text-white/80" />
            </div>
            <div className="flex-1">
              <div
                className="text-base font-medium text-white"
                style={{
                  fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                  letterSpacing: '-0.01em'
                }}
              >
                Real-Time Paper Trading
              </div>
              <div
                className="text-sm text-white/60 mt-1"
                style={{
                  fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                  letterSpacing: '-0.005em'
                }}
              >
                Practice with live market data, zero risk
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-white/[0.02] to-white/[0.01] rounded-xl border border-white/[0.08] shadow-[inset_0_1px_0_rgba(255,255,255,0.02),0_2px_8px_rgba(0,0,0,0.04)]">
            <div className="w-10 h-10 bg-gradient-to-br from-white/[0.08] to-white/[0.03] border border-white/[0.12] rounded-lg flex items-center justify-center shadow-[inset_0_1px_0_rgba(255,255,255,0.05)]">
              <BarChart3 className="w-5 h-5 text-white/80" />
            </div>
            <div className="flex-1">
              <div
                className="text-base font-medium text-white"
                style={{
                  fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                  letterSpacing: '-0.01em'
                }}
              >
                Professional Charts & Tools
              </div>
              <div
                className="text-sm text-white/60 mt-1"
                style={{
                  fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                  letterSpacing: '-0.005em'
                }}
              >
                Advanced indicators and drawing tools
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-white/[0.02] to-white/[0.01] rounded-xl border border-white/[0.08] shadow-[inset_0_1px_0_rgba(255,255,255,0.02),0_2px_8px_rgba(0,0,0,0.04)]">
            <div className="w-10 h-10 bg-gradient-to-br from-white/[0.08] to-white/[0.03] border border-white/[0.12] rounded-lg flex items-center justify-center shadow-[inset_0_1px_0_rgba(255,255,255,0.05)]">
              <Trophy className="w-5 h-5 text-white/80" />
            </div>
            <div className="flex-1">
              <div
                className="text-base font-medium text-white"
                style={{
                  fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                  letterSpacing: '-0.01em'
                }}
              >
                Live Competition Access
              </div>
              <div
                className="text-sm text-white/60 mt-1"
                style={{
                  fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                  letterSpacing: '-0.005em'
                }}
              >
                Compete against other traders for prizes
              </div>
            </div>
          </div>

          {/* Professional Owner Benefits Row */}
          {isWhopOwner && (
            <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-[#FFD700]/[0.03] to-[#FFA500]/[0.02] rounded-xl border border-[#FFD700]/[0.08] shadow-[inset_0_1px_0_rgba(255,215,0,0.02),0_2px_8px_rgba(255,215,0,0.04)]">
              <div className="w-10 h-10 bg-gradient-to-br from-[#FFD700]/[0.15] to-[#FFA500]/[0.08] border border-[#FFD700]/[0.2] rounded-lg flex items-center justify-center shadow-[inset_0_1px_0_rgba(255,215,0,0.1)]">
                <svg className="w-5 h-5 text-[#FFD700]/90" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd"/>
                </svg>
              </div>
              <div className="flex-1">
                <div
                  className="text-base font-medium text-white"
                  style={{
                    fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                    letterSpacing: '-0.01em'
                  }}
                >
                  Affiliate Commission
                </div>
                <div
                  className="text-sm text-[#FFD700]/80 mt-1 font-medium"
                  style={{
                    fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                    letterSpacing: '-0.005em'
                  }}
                >
                  Earn 20% per user signup from your Whop
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Pricing Section with Countdown */}
      <div className="text-center p-4 bg-gradient-to-br from-white/[0.03] to-white/[0.01] rounded-xl border border-white/[0.08] shadow-[inset_0_1px_0_rgba(255,255,255,0.02),0_4px_16px_rgba(0,0,0,0.06)]">
        {isWhopOwner ? (
          <div className="space-y-4">
            {/* FREE */}
            <div
              className="text-4xl font-bold bg-gradient-to-br from-[#FFD700] via-[#FFA500] to-[#FF8C00] bg-clip-text text-transparent"
              style={{
                fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif',
                letterSpacing: '-0.02em',
                textShadow: '0 2px 8px rgba(255,215,0,0.2)'
              }}
            >
              FREE
            </div>

            {/* Competition Starts In */}
            <div className="space-y-3">
              <div
                className="text-xs text-white/60 uppercase tracking-wider"
                style={{
                  fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                  letterSpacing: '0.05em'
                }}
              >
                Competition starts in:
              </div>

              {/* Countdown Timer Boxes */}
              <div className="flex items-center justify-center gap-1 sm:gap-2">
                {/* Days */}
                <div className="text-center">
                  <div
                    className="bg-gradient-to-b from-[#FFD700]/[0.15] to-[#FFA500]/[0.08] border border-[#FFD700]/[0.2] rounded-lg px-2 sm:px-3 py-1.5 sm:py-2 min-w-[40px] sm:min-w-[48px]"
                  >
                    <div
                      className="text-lg font-bold bg-gradient-to-r from-[#FFD700] to-[#FFA500] bg-clip-text text-transparent tabular-nums"
                      style={{
                        fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif',
                        letterSpacing: '-0.02em'
                      }}
                    >
                      {timeLeft.days}
                    </div>
                  </div>
                  <div
                    className="text-xs text-white/50 mt-1"
                    style={{
                      fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                      letterSpacing: '-0.005em'
                    }}
                  >
                    days
                  </div>
                </div>

                {/* Hours */}
                <div className="text-center">
                  <div
                    className="bg-gradient-to-b from-[#FFD700]/[0.15] to-[#FFA500]/[0.08] border border-[#FFD700]/[0.2] rounded-lg px-3 py-2 min-w-[48px]"
                  >
                    <div
                      className="text-lg font-bold bg-gradient-to-r from-[#FFD700] to-[#FFA500] bg-clip-text text-transparent tabular-nums"
                      style={{
                        fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif',
                        letterSpacing: '-0.02em'
                      }}
                    >
                      {String(timeLeft.hours).padStart(2, '0')}
                    </div>
                  </div>
                  <div
                    className="text-xs text-white/50 mt-1"
                    style={{
                      fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                      letterSpacing: '-0.005em'
                    }}
                  >
                    hours
                  </div>
                </div>

                {/* Minutes */}
                <div className="text-center">
                  <div
                    className="bg-gradient-to-b from-[#FFD700]/[0.15] to-[#FFA500]/[0.08] border border-[#FFD700]/[0.2] rounded-lg px-3 py-2 min-w-[48px]"
                  >
                    <div
                      className="text-lg font-bold bg-gradient-to-r from-[#FFD700] to-[#FFA500] bg-clip-text text-transparent tabular-nums"
                      style={{
                        fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif',
                        letterSpacing: '-0.02em'
                      }}
                    >
                      {String(timeLeft.minutes).padStart(2, '0')}
                    </div>
                  </div>
                  <div
                    className="text-xs text-white/50 mt-1"
                    style={{
                      fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                      letterSpacing: '-0.005em'
                    }}
                  >
                    minutes
                  </div>
                </div>

                {/* Seconds */}
                <div className="text-center">
                  <div
                    className="bg-gradient-to-b from-[#FFD700]/[0.15] to-[#FFA500]/[0.08] border border-[#FFD700]/[0.2] rounded-lg px-3 py-2 min-w-[48px]"
                  >
                    <div
                      className="text-lg font-bold bg-gradient-to-r from-[#FFD700] to-[#FFA500] bg-clip-text text-transparent tabular-nums"
                      style={{
                        fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif',
                        letterSpacing: '-0.02em'
                      }}
                    >
                      {String(timeLeft.seconds).padStart(2, '0')}
                    </div>
                  </div>
                  <div
                    className="text-xs text-white/50 mt-1"
                    style={{
                      fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                      letterSpacing: '-0.005em'
                    }}
                  >
                    seconds
                  </div>
                </div>
              </div>
            </div>

            <div
              className="text-base text-white/60"
              style={{
                fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                letterSpacing: '-0.005em'
              }}
            >
              Community Owner Access
            </div>
          </div>
        ) : (
          <div>
            <div
              className="text-3xl font-bold mb-2 text-white"
              style={{
                fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif',
                letterSpacing: '-0.02em',
                textShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}
            >
              $10
            </div>
            <div
              className="text-base text-white/60"
              style={{
                fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                letterSpacing: '-0.005em'
              }}
            >
              One-time competition entry
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons - Compact Sizing for Desktop */}
      <div className="flex gap-3">
        <button
          onClick={handleJoinNow}
          disabled={isProcessingPayment}
          className="flex-1 group relative bg-gradient-to-b from-[#FFD700] to-[#FFA500] hover:from-[#FFED4E] hover:to-[#FFD700] border border-[#FFA500]/[0.6] hover:border-[#FFD700]/[0.8] text-black font-medium py-2 px-3 rounded-lg text-sm transition-all duration-200 backdrop-blur-sm active:scale-[0.98] relative overflow-hidden disabled:opacity-50 disabled:cursor-not-allowed"
          style={{
            fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
            letterSpacing: '-0.01em',
            boxShadow: `
              0 0 12px rgba(255, 215, 0, 0.2),
              0 2px 6px rgba(0, 0, 0, 0.12),
              inset 0 1px 0 rgba(255, 255, 255, 0.4),
              inset 0 -1px 0 rgba(0, 0, 0, 0.1)
            `
          }}
        >
          <span className="relative z-10 font-semibold">
            {isProcessingPayment ? 'Processing...' : 'Join Now'}
          </span>

          {/* Subtle inner highlight */}
          <div className="absolute inset-0 rounded-lg bg-gradient-to-b from-white/20 via-transparent to-transparent pointer-events-none" />
        </button>

        <button
          onClick={handleNotNow}
          className="flex-1 group relative bg-gradient-to-b from-white/[0.08] to-white/[0.03] hover:from-white/[0.12] hover:to-white/[0.06] border border-white/[0.15] hover:border-white/[0.25] font-medium py-2 px-3 rounded-lg text-sm transition-all duration-200 ease-out shadow-[0_2px_6px_rgba(0,0,0,0.1),inset_0_1px_0_rgba(255,255,255,0.1)]"
          style={{
            fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
            letterSpacing: '-0.01em'
          }}
        >
          <span className="relative z-10 text-white/90 font-semibold">
            Not Now
          </span>
          {/* Enhanced inner background transition */}
          <div className="absolute inset-0 rounded-lg bg-gradient-to-b from-white/[0.12] to-white/[0.06] opacity-0 group-hover:opacity-100 transition-opacity duration-200 ease-out" />
        </button>
      </div>
    </motion.div>
  );




  // Force show modal for debugging
  console.log('🎯 TradingOnboardingModal render - isOpen:', isOpen);

  if (!isOpen) {
    console.log('❌ Modal not showing because isOpen is false');
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4">
      <div
        className="max-w-[95vw] sm:max-w-md lg:max-w-lg xl:max-w-xl max-h-[90vh] w-full bg-[#0a0a0a] border border-white/[0.08] text-white rounded-xl focus:outline-none focus:ring-0 shadow-[0_20px_60px_rgba(0,0,0,0.4),inset_0_1px_0_rgba(255,255,255,0.03),inset_0_-1px_0_rgba(0,0,0,0.2)] overflow-y-auto trading-modal-scrollbar"
        style={{
          outline: 'none',
          boxShadow: '0 20px 60px rgba(0,0,0,0.4), inset 0 1px 0 rgba(255,255,255,0.03), inset 0 -1px 0 rgba(0,0,0,0.2)',
          scrollbarWidth: 'thin',
          scrollbarColor: 'rgba(255,255,255,0.2) transparent'
        }}
      >
        <div className="p-4 sm:p-6 lg:p-8 relative">
          {/* X Button for dismissal */}
          {onPreview && (
            <button
              onClick={onPreview}
              className="absolute top-3 right-3 z-10 w-8 h-8 flex items-center justify-center text-white/40 hover:text-white/60 transition-colors rounded-full hover:bg-white/5"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          )}

          {renderCombinedCard()}
        </div>
      </div>
    </div>
  );
};

export default TradingOnboardingModal;
