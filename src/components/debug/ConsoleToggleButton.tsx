import React from 'react';
import { createPortal } from 'react-dom';
import { Terminal } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ConsoleToggleButtonProps {
  onClick: () => void;
  isVisible: boolean;
}

const isMobile = () => {
  if (typeof window !== 'undefined') {
    return window.innerWidth < 768;
  }
  return false;
};

const ConsoleToggleButton: React.FC<ConsoleToggleButtonProps> = ({
  onClick,
  isVisible
}) => {
  const mobile = isMobile();

  const buttonContent = (
    <Button
      className={`
        console-log-viewer fixed bottom-4 left-4 z-[99998]
        ${mobile ? 'w-14 h-14' : 'w-12 h-12'} rounded-full
        bg-[#1A1A1A] hover:bg-[#2A2A2A]
        border border-[#3A3A3A]
        shadow-lg hover:shadow-xl
        transition-all duration-200
        touch-manipulation
        ${isVisible ? 'bg-blue-600 hover:bg-blue-700' : ''}
      `}
      style={{ pointerEvents: 'auto' }}
      onClick={(e) => {
        e.stopPropagation();
        onClick();
      }}
      size="sm"
      title={`${isVisible ? 'Hide' : 'Show'} Console Logs (Ctrl+Shift+C)`}
    >
      <Terminal className={`${mobile ? 'h-6 w-6' : 'h-5 w-5'} ${isVisible ? 'text-white' : 'text-white/70'}`} />
    </Button>
  );

  // Use portal to render at the top level of the DOM
  return typeof document !== 'undefined'
    ? createPortal(buttonContent, document.body)
    : buttonContent;
};

export default ConsoleToggleButton;
