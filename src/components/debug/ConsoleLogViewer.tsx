import React, { useState, useEffect, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { Terminal, X, Trash2, Filter, ChevronDown, ChevronUp, Minimize2, Maximize2, ArrowDown, <PERSON>Up, Pause, Play } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface LogEntry {
  id: string;
  timestamp: Date;
  level: 'log' | 'info' | 'warn' | 'error' | 'debug';
  args: any[];
  message: string;
}

interface ConsoleLogViewerProps {
  isVisible: boolean;
  onToggleVisibility: () => void;
}

const ConsoleLogViewer: React.FC<ConsoleLogViewerProps> = ({
  isVisible,
  onToggleVisibility
}) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [isExpanded, setIsExpanded] = useState(true);
  const [isMinimized, setIsMinimized] = useState(false);
  const [autoScroll, setAutoScroll] = useState(true);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const [isMobile, setIsMobile] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.innerWidth < 768;
    }
    return false;
  });
  const [filterLevel, setFilterLevel] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('consoleViewer.filterLevel');
      return saved || 'all';
    }
    return 'all';
  });
  const [position, setPosition] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('consoleViewer.position');
      const isMobile = window.innerWidth < 768;
      return saved ? JSON.parse(saved) : (isMobile ? { x: 10, y: 10 } : { x: 20, y: 20 });
    }
    return { x: 20, y: 20 };
  });
  const [size, setSize] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('consoleViewer.size');
      const isMobile = window.innerWidth < 768;
      const defaultSize = isMobile
        ? { width: window.innerWidth - 20, height: Math.min(400, window.innerHeight * 0.6) }
        : { width: 600, height: 400 };
      return saved ? JSON.parse(saved) : defaultSize;
    }
    return { width: 600, height: 400 };
  });
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [resizeStart, setResizeStart] = useState({ x: 0, y: 0, width: 0, height: 0 });
  
  const logContainerRef = useRef<HTMLDivElement>(null);
  const windowRef = useRef<HTMLDivElement>(null);
  const originalConsole = useRef<any>({});

  // Auto-scroll to bottom when new logs are added
  const scrollToBottom = useCallback(() => {
    if (logContainerRef.current && autoScroll && !isUserScrolling) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [autoScroll, isUserScrolling]);

  // Check if user is at the bottom of the scroll
  const isAtBottom = useCallback(() => {
    if (!logContainerRef.current) return true;
    const { scrollTop, scrollHeight, clientHeight } = logContainerRef.current;
    return scrollTop + clientHeight >= scrollHeight - 5; // 5px tolerance
  }, []);

  // Handle scroll events
  const handleScroll = useCallback(() => {
    if (!logContainerRef.current) return;

    const atBottom = isAtBottom();

    // If user scrolled to bottom, resume auto-scroll
    if (atBottom && !autoScroll) {
      setAutoScroll(true);
    }
    // If user scrolled up from bottom, pause auto-scroll
    else if (!atBottom && autoScroll) {
      setAutoScroll(false);
    }
  }, [autoScroll, isAtBottom]);

  // Intercept console methods
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Store original console methods
    originalConsole.current = {
      log: console.log,
      info: console.info,
      warn: console.warn,
      error: console.error,
      debug: console.debug,
    };

    const createLogInterceptor = (level: LogEntry['level']) => {
      return (...args: any[]) => {
        // Call original console method
        originalConsole.current[level](...args);

        // Create log entry
        const logEntry: LogEntry = {
          id: Date.now() + Math.random().toString(36),
          timestamp: new Date(),
          level,
          args,
          message: args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
          ).join(' ')
        };

        setLogs(prevLogs => {
          const newLogs = [...prevLogs, logEntry];
          // Keep only last 1000 logs to prevent memory issues
          return newLogs.slice(-1000);
        });
      };
    };

    // Override console methods
    console.log = createLogInterceptor('log');
    console.info = createLogInterceptor('info');
    console.warn = createLogInterceptor('warn');
    console.error = createLogInterceptor('error');
    console.debug = createLogInterceptor('debug');

    return () => {
      // Restore original console methods
      console.log = originalConsole.current.log;
      console.info = originalConsole.current.info;
      console.warn = originalConsole.current.warn;
      console.error = originalConsole.current.error;
      console.debug = originalConsole.current.debug;
    };
  }, []);

  // Auto-scroll when logs change
  useEffect(() => {
    scrollToBottom();
  }, [logs, scrollToBottom]);

  // Add scroll event listener
  useEffect(() => {
    const container = logContainerRef.current;
    if (!container) return;

    let scrollTimeout: NodeJS.Timeout;

    const onScroll = () => {
      setIsUserScrolling(true);

      // Clear existing timeout
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }

      // Set user scrolling to false after scroll stops
      scrollTimeout = setTimeout(() => {
        setIsUserScrolling(false);
      }, 150);

      handleScroll();
    };

    container.addEventListener('scroll', onScroll, { passive: true });

    return () => {
      container.removeEventListener('scroll', onScroll);
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
    };
  }, [handleScroll]);

  // Handle window resize and mobile detection
  useEffect(() => {
    const handleResize = () => {
      const newIsMobile = window.innerWidth < 768;
      setIsMobile(newIsMobile);

      // Adjust size and position for mobile
      if (newIsMobile) {
        setSize(prev => ({
          width: Math.min(prev.width, window.innerWidth - 20),
          height: Math.min(prev.height, window.innerHeight * 0.7)
        }));
        setPosition(prev => ({
          x: Math.max(10, Math.min(prev.x, window.innerWidth - 300)),
          y: Math.max(10, Math.min(prev.y, window.innerHeight - 200))
        }));
      }
    };

    // Initial mobile setup
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);



  // Handle dragging (mouse and touch)
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget || (e.target as HTMLElement).classList.contains('drag-handle')) {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      });
    }
  }, [position]);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (e.target === e.currentTarget || (e.target as HTMLElement).classList.contains('drag-handle')) {
      const touch = e.touches[0];
      setIsDragging(true);
      setDragStart({
        x: touch.clientX - position.x,
        y: touch.clientY - position.y
      });
    }
  }, [position]);

  const handleMouseMove = useCallback((e: MouseEvent | TouchEvent) => {
    const clientX = 'touches' in e ? e.touches[0]?.clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0]?.clientY : e.clientY;

    if (!clientX || !clientY) return;

    if (isDragging) {
      const newPosition = {
        x: Math.max(0, Math.min(window.innerWidth - 100, clientX - dragStart.x)),
        y: Math.max(0, Math.min(window.innerHeight - 50, clientY - dragStart.y))
      };
      setPosition(newPosition);
      localStorage.setItem('consoleViewer.position', JSON.stringify(newPosition));
    }
    if (isResizing) {
      const minWidth = isMobile ? 280 : 300;
      const minHeight = isMobile ? 150 : 200;
      const maxWidth = isMobile ? window.innerWidth - 20 : window.innerWidth - 100;
      const maxHeight = isMobile ? window.innerHeight * 0.8 : window.innerHeight - 100;

      const newWidth = Math.max(minWidth, Math.min(maxWidth, resizeStart.width + (clientX - resizeStart.x)));
      const newHeight = Math.max(minHeight, Math.min(maxHeight, resizeStart.height + (clientY - resizeStart.y)));
      const newSize = { width: newWidth, height: newHeight };
      setSize(newSize);
      localStorage.setItem('consoleViewer.size', JSON.stringify(newSize));
    }
  }, [isDragging, isResizing, dragStart, resizeStart, isMobile]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsResizing(false);
  }, []);

  useEffect(() => {
    if (isDragging || isResizing) {
      const handleMove = (e: MouseEvent | TouchEvent) => handleMouseMove(e);
      const handleEnd = () => handleMouseUp();

      document.addEventListener('mousemove', handleMove);
      document.addEventListener('mouseup', handleEnd);
      document.addEventListener('touchmove', handleMove, { passive: false });
      document.addEventListener('touchend', handleEnd);

      return () => {
        document.removeEventListener('mousemove', handleMove);
        document.removeEventListener('mouseup', handleEnd);
        document.removeEventListener('touchmove', handleMove);
        document.removeEventListener('touchend', handleEnd);
      };
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp]);

  // Handle resize (mouse and touch)
  const handleResizeStart = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    setIsResizing(true);
    setResizeStart({
      x: clientX,
      y: clientY,
      width: size.width,
      height: size.height
    });
  }, [size]);

  const clearLogs = useCallback(() => {
    setLogs([]);
  }, []);

  const scrollToTop = useCallback(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = 0;
      setAutoScroll(false);
    }
  }, []);

  const forceScrollToBottom = useCallback(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
      setAutoScroll(true);
    }
  }, []);

  const toggleAutoScroll = useCallback(() => {
    setAutoScroll(prev => {
      const newValue = !prev;
      if (newValue) {
        // If enabling auto-scroll, scroll to bottom
        setTimeout(() => scrollToBottom(), 100);
      }
      return newValue;
    });
  }, [scrollToBottom]);

  // Keyboard shortcuts for scrolling
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle shortcuts when console is visible and focused
      if (!isVisible || !windowRef.current?.contains(document.activeElement)) return;

      switch (e.key) {
        case 'Home':
          e.preventDefault();
          scrollToTop();
          break;
        case 'End':
          e.preventDefault();
          forceScrollToBottom();
          break;
        case 'PageUp':
          e.preventDefault();
          if (logContainerRef.current) {
            logContainerRef.current.scrollTop -= logContainerRef.current.clientHeight * 0.8;
            setAutoScroll(false);
          }
          break;
        case 'PageDown':
          e.preventDefault();
          if (logContainerRef.current) {
            logContainerRef.current.scrollTop += logContainerRef.current.clientHeight * 0.8;
          }
          break;
        case ' ':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            toggleAutoScroll();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isVisible, scrollToTop, forceScrollToBottom, toggleAutoScroll]);

  const getLogLevelColor = (level: LogEntry['level']) => {
    switch (level) {
      case 'error': return 'text-red-400 bg-red-400/10 border-red-400/20';
      case 'warn': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      case 'info': return 'text-blue-400 bg-blue-400/10 border-blue-400/20';
      case 'debug': return 'text-purple-400 bg-purple-400/10 border-purple-400/20';
      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  const getLogLevelIcon = (level: LogEntry['level']) => {
    switch (level) {
      case 'error': return '❌';
      case 'warn': return '⚠️';
      case 'info': return 'ℹ️';
      case 'debug': return '🐛';
      default: return '📝';
    }
  };

  const filteredLogs = logs.filter(log => 
    filterLevel === 'all' || log.level === filterLevel
  );

  if (!isVisible) return null;

  const consoleContent = (
    <div
      ref={windowRef}
      className={`console-log-viewer fixed z-[99999] bg-[#0A0A0A] border border-[#1A1A1A]/50 rounded-lg shadow-2xl select-none ${
        isMobile ? 'touch-manipulation' : ''
      }`}
      style={{
        left: position.x,
        top: position.y,
        width: isMinimized ? 'auto' : size.width,
        height: isMinimized ? 'auto' : size.height,
        minWidth: isMinimized ? 'auto' : (isMobile ? 280 : 300),
        minHeight: isMinimized ? 'auto' : (isMobile ? 150 : 200),
        maxWidth: isMobile ? window.innerWidth - 20 : 'none',
        maxHeight: isMobile ? window.innerHeight * 0.9 : 'none',
        pointerEvents: 'auto', // Ensure pointer events work
      }}
      onClick={(e) => e.stopPropagation()} // Prevent modal overlay clicks from affecting this
      onMouseDown={(e) => e.stopPropagation()} // Prevent event bubbling
      onTouchStart={(e) => e.stopPropagation()} // Prevent touch event bubbling
    >
      {/* Header */}
      <div
        className={`flex items-center justify-between p-3 border-b border-[#1A1A1A]/30 cursor-move drag-handle ${
          isMobile ? 'touch-manipulation' : ''
        }`}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
      >
        <div className="flex items-center gap-2">
          <Terminal className="h-4 w-4 text-white/70" />
          <span className="text-sm font-medium text-white/90">Console Logs</span>
          <Badge variant="secondary" className="text-xs">
            {filteredLogs.length}
          </Badge>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMinimized(!isMinimized)}
            className="h-6 w-6 p-0 hover:bg-white/[0.05]"
          >
            {isMinimized ? (
              <Maximize2 className="h-3 w-3 text-white/60" />
            ) : (
              <Minimize2 className="h-3 w-3 text-white/60" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-6 w-6 p-0 hover:bg-white/[0.05]"
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3 text-white/60" />
            ) : (
              <ChevronUp className="h-3 w-3 text-white/60" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleVisibility}
            className="h-6 w-6 p-0 hover:bg-white/[0.05]"
          >
            <X className="h-3 w-3 text-white/60" />
          </Button>
        </div>
      </div>

      {/* Content */}
      {!isMinimized && isExpanded && (
        <div className="flex flex-col h-full">
          {/* Controls */}
          <div className={`flex items-center justify-between p-2 border-b border-[#1A1A1A]/20 ${
            isMobile ? 'flex-wrap gap-2' : ''
          }`}>
            <div className="flex items-center gap-2">
              <Filter className="h-3 w-3 text-white/60" />
              <select
                value={filterLevel}
                onChange={(e) => {
                  const newValue = e.target.value;
                  setFilterLevel(newValue);
                  localStorage.setItem('consoleViewer.filterLevel', newValue);
                }}
                className={`bg-[#1A1A1A] border border-[#2A2A2A] rounded px-2 py-1 text-xs text-white/90 ${
                  isMobile ? 'min-h-[44px] text-base' : ''
                }`}
              >
                <option value="all">All</option>
                <option value="log">Log</option>
                <option value="info">Info</option>
                <option value="warn">Warn</option>
                <option value="error">Error</option>
                <option value="debug">Debug</option>
              </select>
            </div>

            {/* Scroll Controls */}
            <div className={`flex items-center gap-1 ${isMobile ? 'w-full justify-center mt-2' : ''}`}>
              <Button
                variant="ghost"
                size="sm"
                onClick={scrollToTop}
                className={`${isMobile ? 'h-10 w-10' : 'h-6 w-6'} p-0 hover:bg-white/[0.05]`}
                title="Scroll to top"
              >
                <ArrowUp className={`${isMobile ? 'h-4 w-4' : 'h-3 w-3'}`} />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={forceScrollToBottom}
                className={`${isMobile ? 'h-10 w-10' : 'h-6 w-6'} p-0 hover:bg-white/[0.05]`}
                title="Scroll to bottom"
              >
                <ArrowDown className={`${isMobile ? 'h-4 w-4' : 'h-3 w-3'}`} />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleAutoScroll}
                className={`${isMobile ? 'h-10 w-10' : 'h-6 w-6'} p-0 hover:bg-white/[0.05] ${autoScroll ? 'text-green-400' : 'text-white/60'}`}
                title={autoScroll ? 'Auto-scroll enabled' : 'Auto-scroll disabled'}
              >
                {autoScroll ? <Play className={`${isMobile ? 'h-4 w-4' : 'h-3 w-3'}`} /> : <Pause className={`${isMobile ? 'h-4 w-4' : 'h-3 w-3'}`} />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearLogs}
                className={`${isMobile ? 'h-10 px-4 text-sm' : 'h-6 px-2 text-xs'} hover:bg-white/[0.05]`}
              >
                <Trash2 className={`${isMobile ? 'h-4 w-4 mr-2' : 'h-3 w-3 mr-1'}`} />
                Clear
              </Button>
            </div>
          </div>

          {/* Logs */}
          <div className="flex-1 relative">
            <div
              ref={logContainerRef}
              className="h-full overflow-y-auto p-2 space-y-1 text-xs font-mono scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent hover:scrollbar-thumb-white/30"
              style={{ maxHeight: size.height - 120 }}
            >
            {filteredLogs.length === 0 ? (
              <div className="text-white/40 text-center py-12">
                <Terminal className="h-8 w-8 mx-auto mb-3 opacity-50" />
                <p className="text-sm">No logs to display</p>
                <p className="text-xs mt-1 opacity-70">
                  {filterLevel === 'all'
                    ? 'Console logs will appear here as they are generated'
                    : `No ${filterLevel} logs found`
                  }
                </p>
              </div>
            ) : (
              filteredLogs.map((log) => (
                <div key={log.id} className="flex gap-2 py-2 px-3 rounded-md hover:bg-white/[0.02] border-l-2 border-transparent hover:border-white/10">
                  <span className="text-white/40 shrink-0 text-xs">
                    {log.timestamp.toLocaleTimeString()}
                  </span>
                  <span className="shrink-0 text-sm">
                    {getLogLevelIcon(log.level)}
                  </span>
                  <Badge
                    variant="secondary"
                    className={`shrink-0 text-xs px-2 py-0.5 border ${getLogLevelColor(log.level)}`}
                  >
                    {log.level.toUpperCase()}
                  </Badge>
                  <span className="text-white/80 break-all leading-relaxed">
                    {log.message}
                  </span>
                </div>
              ))
            )}
            </div>

            {/* Scroll Indicator */}
            {!autoScroll && (
              <div className="absolute bottom-2 right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full shadow-lg animate-pulse">
                Auto-scroll paused
              </div>
            )}
          </div>
        </div>
      )}

      {/* Resize handle */}
      {!isMinimized && (
        <div
          className={`absolute bottom-0 right-0 ${
            isMobile ? 'w-6 h-6' : 'w-4 h-4'
          } cursor-se-resize opacity-50 hover:opacity-100 touch-manipulation`}
          onMouseDown={handleResizeStart}
          onTouchStart={handleResizeStart}
        >
          <div className={`absolute bottom-1 right-1 ${
            isMobile ? 'w-3 h-3' : 'w-2 h-2'
          } border-r-2 border-b-2 border-white/40`}></div>
        </div>
      )}
    </div>
  );

  // Use portal to render at the top level of the DOM
  return typeof document !== 'undefined'
    ? createPortal(consoleContent, document.body)
    : consoleContent;
};

export default ConsoleLogViewer;
