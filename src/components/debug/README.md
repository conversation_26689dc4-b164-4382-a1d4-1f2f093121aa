# Console Log Viewer

A floating, draggable console log viewer that captures and displays all console output in a convenient on-screen window.

## Features

- **Real-time Log Capture**: Intercepts all console.log, console.info, console.warn, console.error, and console.debug calls
- **Floating Window**: Draggable and resizable window that stays on top
- **Log Filtering**: Filter logs by level (All, Log, Info, Warn, Error, Debug)
- **Timestamps**: Each log entry shows the exact time it was logged
- **Color Coding**: Different colors for different log levels
- **Smart Auto-scroll**: Automatically scrolls to show the latest logs, pauses when you scroll up manually
- **Scroll Controls**: Buttons to scroll to top, bottom, and toggle auto-scroll
- **Keyboard Navigation**: Home/End, PageUp/PageDown, Ctrl+Space shortcuts
- **Scroll Indicators**: Visual feedback when auto-scroll is paused
- **Custom Scrollbar**: Styled scrollbar for better visibility
- **Mobile-Responsive**: Automatically adapts to mobile screens with touch support
- **Touch Controls**: Full touch support for dragging, resizing, and scrolling
- **Memory Management**: Keeps only the last 1000 logs to prevent memory issues
- **Minimize/Maximize**: Can be minimized to save screen space
- **Clear Function**: Button to clear all logs

## Usage

### Keyboard Shortcuts
- **Ctrl+Shift+C** (or **Cmd+Shift+C** on Mac): Toggle console visibility
- **F12**: Alternative shortcut to toggle console visibility

#### Scrolling Shortcuts (when console is focused)
- **Home**: Scroll to top
- **End**: Scroll to bottom
- **Page Up**: Scroll up one page
- **Page Down**: Scroll down one page
- **Ctrl+Space** (or **Cmd+Space**): Toggle auto-scroll on/off

### Toggle Button
- Click the floating terminal icon button in the bottom-left corner to show/hide the console

### Browser Console Functions
The following functions are available in the browser console:
- `showConsoleViewer()`: Show the console viewer
- `hideConsoleViewer()`: Hide the console viewer
- `toggleConsoleViewer()`: Toggle console visibility
- `testConsole()`: Generate test logs of all levels
- `testScrolling()`: Generate many logs to test scrolling functionality
- `resetConsoleForMobile()`: Reset console size/position for mobile devices

### Window Controls
- **Drag**: Click and drag the header to move the window
- **Resize**: Drag the resize handle in the bottom-right corner
- **Minimize**: Click the minimize button to collapse the window
- **Expand/Collapse**: Click the chevron button to show/hide the log content
- **Close**: Click the X button to hide the console viewer

### Scroll Controls
- **↑ Button**: Scroll to top of logs
- **↓ Button**: Scroll to bottom of logs
- **▶/⏸ Button**: Toggle auto-scroll on/off (green when active)
- **Auto-scroll Behavior**:
  - Automatically scrolls to show new logs
  - Pauses when you manually scroll up
  - Resumes when you scroll back to bottom
  - Shows "Auto-scroll paused" indicator when disabled

### Log Filtering
Use the dropdown in the controls bar to filter logs by level:
- **All**: Show all log levels
- **Log**: Show only console.log messages
- **Info**: Show only console.info messages
- **Warn**: Show only console.warn messages
- **Error**: Show only console.error messages
- **Debug**: Show only console.debug messages

## Implementation Details

The console log viewer works by:
1. Intercepting the native console methods
2. Storing the original console functions
3. Calling the original functions (so logs still appear in browser console)
4. Capturing the log data and displaying it in the floating window
5. Restoring original console methods when the component unmounts

## Components

- `ConsoleLogViewer.tsx`: Main floating window component
- `ConsoleToggleButton.tsx`: Floating toggle button
- `useConsoleLogViewer.ts`: Hook for managing console state and keyboard shortcuts

## Testing

To test the console viewer:
1. Open the application
2. Press **Ctrl+Shift+C** or click the terminal button to show the console
3. Open browser dev tools and run: `testConsole()`
4. You should see test logs appear in both the browser console and the floating viewer
5. Try filtering by different log levels
6. Test dragging, resizing, and minimizing the window
7. Run `testScrolling()` to generate many logs and test scrolling features:
   - Scroll up manually to pause auto-scroll
   - Use scroll control buttons
   - Try keyboard shortcuts (Home, End, Page Up/Down)
   - Notice the "Auto-scroll paused" indicator
