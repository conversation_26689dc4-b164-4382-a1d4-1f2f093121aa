import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { getPriceIdForPlanType, isLegacyUser } from '@/utils/planUtils';
import { getAffiliateCode } from '@/utils/growiUtils';

const DirectCheckout = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleDirectCheckout = async () => {
      try {
        // Check if user is authenticated
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          // Redirect to login/onboarding if not authenticated
          navigate('/');
          return;
        }

        // Get user creation date for price ID selection
        const { data: profile } = await supabase
          .from('profiles')
          .select('created_at')
          .eq('id', user.id)
          .single();

        const userCreatedAt = profile?.created_at || user.created_at;

        // Parse URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const premiumNoTrial = urlParams.get('premiumnotrial') !== null;
        const annual = urlParams.get('annual') !== null;

        // Determine billing period and price ID
        const billingPeriod = annual ? 'yearly' : 'weekly';
        const priceId = getPriceIdForPlanType('premium', billingPeriod);

      

        // Get affiliate code for Growi tracking
        const affiliateCode = getAffiliateCode();

        // Create checkout session
        const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-checkout`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
          },
          body: JSON.stringify({
            action: 'create-checkout-session',
            priceId,
            returnUrl: window.location.origin + '/subscription/manage?success=true',
            skipTrial: premiumNoTrial,
            affiliateCode
          })
        });

        if (!response.ok) {
          throw new Error(`Server returned ${response.status}`);
        }

        const data = await response.json();

        if (data.error) {
          throw new Error(data.error);
        }

        // Redirect to Stripe checkout
        if (data.url) {
          window.location.href = data.url;
        } else {
          throw new Error('No checkout URL returned');
        }

      } catch (err) {
        console.error('Direct checkout error:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        setIsLoading(false);
      }
    };

    handleDirectCheckout();
  }, [navigate]);

  if (error) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center">
        <div className="max-w-md mx-auto text-center px-8">
          <div className="w-16 h-16 mx-auto mb-6 bg-red-500/20 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h1 className="text-2xl font-normal text-white mb-4 font-sans">
            Checkout Error
          </h1>
          <p className="text-white/60 text-base font-sans leading-relaxed mb-6">
            {error}
          </p>
          <button
            onClick={() => navigate('/subscription')}
            className="px-6 py-3 bg-white hover:bg-white/95 text-black rounded-lg transition-colors duration-200 font-sans"
          >
            View Plans
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center">
      <div className="max-w-md mx-auto text-center px-8">
        <div className="w-16 h-16 mx-auto mb-6 bg-white/[0.02] rounded-full flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-white/20 border-t-white/60 rounded-full animate-spin"></div>
        </div>
        <h1 className="text-2xl font-normal text-white mb-4 font-sans">
          Redirecting to Checkout
        </h1>
        <p className="text-white/60 text-base font-sans leading-relaxed">
          Please wait while we prepare your Premium subscription checkout...
        </p>
      </div>
    </div>
  );
};

export default DirectCheckout;
