import React from 'react';
import { useProductionLock, useFeatureAvailable, useProductionLockProps } from '@/hooks/useProductionLock';
import { ENV } from '@/config/env';

/**
 * Test component to verify production lock functionality
 * This component can be temporarily added to test the production lock features
 */
const ProductionLockTest: React.FC = () => {
  const isLocked = useProductionLock();
  const competitionsAvailable = useFeatureAvailable('competitions');
  const analyticsAvailable = useFeatureAvailable('analytics');
  const createCompetitionAvailable = useFeatureAvailable('create-competition');
  const tradingAvailable = useFeatureAvailable('trading');
  
  const competitionProps = useProductionLockProps('competitions');
  const analyticsProps = useProductionLockProps('analytics');
  const createCompetitionProps = useProductionLockProps('create-competition');
  const tradingProps = useProductionLockProps('trading');

  return (
    <div className="p-6 bg-[#141414] border border-white/10 rounded-lg max-w-2xl mx-auto mt-8">
      <h2 className="text-xl font-bold text-white mb-4">Production Lock Test</h2>
      
      <div className="space-y-4">
        <div className="bg-[#0A0A0A] p-4 rounded border border-white/5">
          <h3 className="text-lg font-semibold text-white mb-2">Environment Status</h3>
          <div className="space-y-1 text-sm">
            <div className="text-white/70">
              <strong>Hostname:</strong> {typeof window !== 'undefined' ? window.location.hostname : 'N/A'}
            </div>
            <div className="text-white/70">
              <strong>Mode:</strong> {ENV.mode}
            </div>
            <div className="text-white/70">
              <strong>Is Production:</strong> {ENV.isProduction ? 'Yes' : 'No'}
            </div>
            <div className="text-white/70">
              <strong>Is Development:</strong> {ENV.isDevelopment ? 'Yes' : 'No'}
            </div>
            <div className="text-white/70">
              <strong>Production Lock Enabled:</strong> {isLocked ? 'Yes' : 'No'}
            </div>
            <div className="text-white/70">
              <strong>VITE_PRODUCTION_LOCK:</strong> {import.meta.env.VITE_PRODUCTION_LOCK || 'undefined'}
            </div>
            <div className="text-white/70">
              <strong>Is Whop Domain:</strong> {typeof window !== 'undefined' && (window.location.hostname.includes('whop.com') || window.location.hostname.includes('whop.io')) ? 'Yes' : 'No'}
            </div>
          </div>
        </div>

        <div className="bg-[#0A0A0A] p-4 rounded border border-white/5">
          <h3 className="text-lg font-semibold text-white mb-2">Feature Availability</h3>
          <div className="space-y-1 text-sm">
            <div className={`${competitionsAvailable ? 'text-green-400' : 'text-red-400'}`}>
              <strong>Competitions:</strong> {competitionsAvailable ? 'Available' : 'Locked'}
            </div>
            <div className={`${analyticsAvailable ? 'text-green-400' : 'text-red-400'}`}>
              <strong>Analytics:</strong> {analyticsAvailable ? 'Available' : 'Locked'}
            </div>
            <div className={`${createCompetitionAvailable ? 'text-green-400' : 'text-red-400'}`}>
              <strong>Create Competition:</strong> {createCompetitionAvailable ? 'Available' : 'Locked'}
            </div>
            <div className={`${tradingAvailable ? 'text-green-400' : 'text-red-400'}`}>
              <strong>Trading:</strong> {tradingAvailable ? 'Available' : 'Locked'}
            </div>
          </div>
        </div>

        <div className="bg-[#0A0A0A] p-4 rounded border border-white/5">
          <h3 className="text-lg font-semibold text-white mb-2">Button Tests</h3>
          <div className="space-y-2">
            <button
              className="px-4 py-2 bg-blue-600 text-white rounded mr-2"
              {...competitionProps}
            >
              Competitions Button
            </button>
            <button
              className="px-4 py-2 bg-green-600 text-white rounded mr-2"
              {...analyticsProps}
            >
              Analytics Button
            </button>
            <button
              className="px-4 py-2 bg-purple-600 text-white rounded mr-2"
              {...createCompetitionProps}
            >
              Create Competition Button
            </button>
            <button
              className="px-4 py-2 bg-orange-600 text-white rounded"
              {...tradingProps}
            >
              Trading Button
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductionLockTest;
