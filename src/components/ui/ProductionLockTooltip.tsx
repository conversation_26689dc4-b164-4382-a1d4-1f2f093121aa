import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface ProductionLockTooltipProps {
  children: React.ReactElement;
  tooltip?: string;
  disabled?: boolean;
}

const ProductionLockTooltip: React.FC<ProductionLockTooltipProps> = ({ 
  children, 
  tooltip, 
  disabled = false 
}) => {
  const [isVisible, setIsVisible] = useState(false);

  if (!disabled || !tooltip) {
    return children;
  }

  return (
    <div 
      className="relative inline-block"
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.9 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-1/2 transform -translate-x-1/2 mt-3 z-50"
          >
            <div className="bg-[#1A1A1A] border border-white/20 rounded-lg px-3 py-2 shadow-xl backdrop-blur-sm">
              <div className="text-white/90 text-sm font-medium whitespace-nowrap">
                {tooltip}
              </div>
              {/* Arrow pointing up */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2">
                <div className="w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-[#1A1A1A]"></div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ProductionLockTooltip;
