import React, { useState, useCallback } from 'react';
import { handleRRDrag } from './RiskRewardTool';

export interface DragState {
  isDragging: boolean;
  dragType: 'tool' | 'handle' | null;
  drawingIndex: number;
  pointIndex?: number;
  handleType?: string; // For RR tools: 'entry', 'stop', 'target', 'resize'
  startPosition: { x: number; y: number } | null;
  currentPosition: { x: number; y: number } | null;
  startChartCoords?: { time: string; price: number } | null;
  isChartDisabled?: boolean;
}

export interface DraggableToolSystemProps {
  drawings: any[];
  onUpdateDrawing: (index: number, updatedDrawing: any) => void;
  chartRef: React.RefObject<any>;
  chartBounds?: any;
}

export const useDraggableTools = ({
  drawings,
  onUpdateDrawing,
  chartRef,
  chartBounds
}: DraggableToolSystemProps) => {
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    dragType: null,
    drawingIndex: -1,
    handleType: undefined,
    startPosition: null,
    currentPosition: null,
    startChartCoords: null,
    isChartDisabled: false
  });

  // Convert pixel coordinates to chart coordinates
  const pixelToChartCoords = useCallback((pixelX: number, pixelY: number) => {
    if (!chartRef.current) return null;

    try {
      // chartRef.current IS the ECharts instance directly
      const chart = chartRef.current;
      const convertedPoint = chart.convertFromPixel('grid', [pixelX, pixelY]);

      if (!convertedPoint || convertedPoint.length < 2) return null;

      // Handle time conversion properly
      const timeValue = convertedPoint[0];
      const time = typeof timeValue === 'number' ? new Date(timeValue).toISOString() : timeValue;

      return {
        time: time,
        price: convertedPoint[1]
      };
    } catch (error) {
      console.error('Error converting pixel to chart coordinates:', error);
      return null;
    }
  }, [chartRef]);

  // Convert chart coordinates to pixel coordinates
  const chartToPixelCoords = useCallback((time: string, price: number) => {
    if (!chartRef.current) return null;

    try {
      // chartRef.current IS the ECharts instance directly
      const chart = chartRef.current;
      const timeValue = new Date(time).getTime();
      const pixelPoint = chart.convertToPixel('grid', [timeValue, price]);

      if (!pixelPoint || pixelPoint.length < 2) return null;

      return {
        x: pixelPoint[0],
        y: pixelPoint[1]
      };
    } catch (error) {
      console.error('Error converting chart to pixel coordinates:', error);
      return null;
    }
  }, [chartRef]);

  // Disable chart interactions completely during tool drag
  const disableChartInteractions = useCallback(() => {
    if (!chartRef.current) return;

    // chartRef.current IS the ECharts instance directly
    const chartInstance = chartRef.current;
    if (!chartInstance) return;

    console.log('🚫 Disabling chart interactions');

    // Get current zoom state to preserve it
    const currentOption = chartInstance.getOption();
    const currentZoom = currentOption?.dataZoom?.[0];

    // Completely disable all chart interactions
    chartInstance.setOption({
      dataZoom: [{
        type: 'inside',
        xAxisIndex: [0],
        start: currentZoom?.start || 85,
        end: currentZoom?.end || 100,
        disabled: true, // Completely disable
        zoomOnMouseWheel: false,
        moveOnMouseMove: false,
        preventDefaultMouseMove: true,
        zoomLock: true,
        realtime: false
      }]
    });

    // Don't disable pointer events on the chart DOM as it prevents our mouse events
    // The dataZoom disable above should be sufficient to prevent chart interactions
  }, [chartRef]);

  // Re-enable chart interactions after drag ends
  const enableChartInteractions = useCallback(() => {
    if (!chartRef.current) return;

    // chartRef.current IS the ECharts instance directly
    const chartInstance = chartRef.current;
    if (!chartInstance) return;

    console.log('✅ Re-enabling chart interactions');

    // Get current zoom state to preserve it
    const currentOption = chartInstance.getOption();
    const currentZoom = currentOption?.dataZoom?.[0];

    // Restore full chart functionality
    chartInstance.setOption({
      dataZoom: [{
        type: 'inside',
        xAxisIndex: [0],
        start: currentZoom?.start || 85,
        end: currentZoom?.end || 100,
        disabled: false,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
        preventDefaultMouseMove: false,
        zoomLock: false,
        realtime: true,
        throttle: 0
      }]
    });
  }, [chartRef]);

  // Start dragging a tool or handle
  const startDrag = useCallback((
    event: MouseEvent,
    drawingIndex: number,
    dragType: 'tool' | 'handle',
    pointIndex?: number,
    handleType?: string
  ) => {
    console.log('🚀 Starting drag:', { drawingIndex, dragType, handleType });

    // IMMEDIATELY prevent all default behaviors
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();

    // chartRef.current IS the ECharts instance directly
    const rect = chartRef.current?.getDom()?.getBoundingClientRect();
    if (!rect) {
      console.log('❌ No chart rect found');
      return;
    }

    const startPos = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    };

    // Convert to chart coordinates for anchoring
    const startChartCoords = pixelToChartCoords(startPos.x, startPos.y);

    console.log('📍 Start position:', { pixel: startPos, chart: startChartCoords });

    setDragState({
      isDragging: true,
      dragType,
      drawingIndex,
      pointIndex,
      handleType,
      startPosition: startPos,
      currentPosition: startPos,
      startChartCoords,
      isChartDisabled: true
    });

    // Disable chart interactions immediately
    disableChartInteractions();

    // Add global mouse event listeners with passive: false
    console.log('📎 Adding mouse event listeners');
    document.addEventListener('mousemove', handleMouseMove, { passive: false });
    document.addEventListener('mouseup', handleMouseUp, { passive: false });

    // Also add pointer events for better touch support
    document.addEventListener('pointermove', handleMouseMove, { passive: false });
    document.addEventListener('pointerup', handleMouseUp, { passive: false });

    // Prevent any chart events from firing
    document.addEventListener('wheel', preventChartEvents, { passive: false, capture: true });
    document.addEventListener('mousedown', preventChartEvents, { passive: false, capture: true });

    console.log('✅ Drag state set and event listeners added');

    // Test if mouse events work at all
    const testMouseMove = (e: MouseEvent) => {
      console.log('🧪 Global mouse move test:', { x: e.clientX, y: e.clientY });
    };
    document.addEventListener('mousemove', testMouseMove);
    setTimeout(() => {
      document.removeEventListener('mousemove', testMouseMove);
    }, 5000); // Remove after 5 seconds
  }, [chartRef, pixelToChartCoords, disableChartInteractions]);

  // Prevent chart events during tool interaction
  const preventChartEvents = useCallback((event: Event) => {
    if (dragState.isDragging) {
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();
    }
  }, [dragState.isDragging]);

  // Handle mouse move during drag - optimized for real-time updates
  const handleMouseMove = useCallback((event: MouseEvent | PointerEvent) => {
    if (!dragState.isDragging || !dragState.startPosition) return;

    console.log('🖱️ Mouse move during drag:', {
      isDragging: dragState.isDragging,
      drawingIndex: dragState.drawingIndex,
      handleType: dragState.handleType
    });

    // Prevent all default behaviors during drag
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();

    // chartRef.current IS the ECharts instance directly
    const rect = chartRef.current?.getDom()?.getBoundingClientRect();
    if (!rect) return;

    const currentPos = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    };

    // Throttle updates for performance (60fps max)
    const now = Date.now();
    if (!handleMouseMove.lastUpdate || now - handleMouseMove.lastUpdate > 16) {
      handleMouseMove.lastUpdate = now;

      setDragState(prev => ({
        ...prev,
        currentPosition: currentPos
      }));

      // Update drawing in real-time with immediate visual feedback
      updateDrawingPosition(currentPos);
    }
  }, [dragState, chartRef]);

  // Add lastUpdate property to the function
  (handleMouseMove as any).lastUpdate = 0;

  // Handle mouse up to end drag
  const handleMouseUp = useCallback((event: MouseEvent | PointerEvent) => {
    if (!dragState.isDragging) return;

    console.log('🏁 Ending drag');

    // Prevent default behaviors
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();

    // Remove ALL event listeners
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('pointermove', handleMouseMove);
    document.removeEventListener('pointerup', handleMouseUp);
    document.removeEventListener('wheel', preventChartEvents, { capture: true } as any);
    document.removeEventListener('mousedown', preventChartEvents, { capture: true } as any);

    // Final position update
    // chartRef.current IS the ECharts instance directly
    const rect = chartRef.current?.getDom()?.getBoundingClientRect();
    if (rect) {
      const finalPos = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      };
      updateDrawingPosition(finalPos, true);
    }

    // Re-enable chart interactions with a small delay
    setTimeout(() => {
      enableChartInteractions();
    }, 50);

    // Reset drag state
    setDragState({
      isDragging: false,
      dragType: null,
      drawingIndex: -1,
      handleType: undefined,
      startPosition: null,
      currentPosition: null,
      startChartCoords: null,
      isChartDisabled: false
    });
  }, [dragState, chartRef, enableChartInteractions, preventChartEvents]);

  // Update drawing position based on drag
  const updateDrawingPosition = useCallback((
    currentPos: { x: number; y: number },
    isFinal: boolean = false
  ) => {
    if (!dragState.startPosition || dragState.drawingIndex < 0) return;

    const drawing = drawings[dragState.drawingIndex];
    if (!drawing) return;

    // Handle RR tools specifically
    if (drawing.type === 'rr') {
      const newCoords = pixelToChartCoords(currentPos.x, currentPos.y);
      if (!newCoords) {
        console.log('❌ Failed to convert pixel coordinates for RR tool');
        return;
      }

      // Get the handle type from the series name or stored data
      const handleType = dragState.handleType || 'move_tool'; // Default to move_tool for main drag area

      console.log('🎯 RR Tool drag update:', {
        handleType,
        newPrice: newCoords.price,
        newTime: newCoords.time,
        currentPos,
        pixelDelta: {
          x: currentPos.x - dragState.startPosition.x,
          y: currentPos.y - dragState.startPosition.y
        }
      });

      // For resize handles, ensure we're providing both price and time coordinates
      let timeCoord = newCoords.time;

      // For horizontal resize handles, prioritize time coordinate conversion
      if (handleType === 'resize_left' || handleType === 'resize_right' ||
          handleType.includes('corner')) {
        // Ensure time coordinate is properly converted
        timeCoord = newCoords.time;
      }

      // Call the RR drag handler with proper coordinates
      const updatedDrawing = handleRRDrag(drawing, handleType, newCoords.price, timeCoord);
      if (updatedDrawing && updatedDrawing !== drawing) {
        console.log('✅ RR Tool updated successfully:', {
          oldEntry: drawing.rrData?.entry,
          newEntry: updatedDrawing.rrData?.entry,
          handleType
        });

        // Force immediate update
        onUpdateDrawing(dragState.drawingIndex, updatedDrawing);

        // Also trigger a chart refresh to ensure visual update
        if (chartRef.current) {
          // chartRef.current IS the ECharts instance directly
          const chartInstance = chartRef.current;
          if (chartInstance) {
            // Force chart to re-render the updated drawing
            setTimeout(() => {
              chartInstance.resize();
            }, 0);
          }
        }
      } else {
        console.log('❌ RR Tool update failed or no change detected');
      }
      return;
    }

    // Handle regular drawing tools
    const deltaX = currentPos.x - dragState.startPosition.x;
    const deltaY = currentPos.y - dragState.startPosition.y;

    if (dragState.dragType === 'handle' && dragState.pointIndex !== undefined) {
      // Update specific point
      const newCoords = pixelToChartCoords(currentPos.x, currentPos.y);
      if (!newCoords) return;

      const updatedDrawing = {
        ...drawing,
        points: drawing.points.map((point: any, index: number) =>
          index === dragState.pointIndex ? newCoords : point
        )
      };

      onUpdateDrawing(dragState.drawingIndex, updatedDrawing);
    } else if (dragState.dragType === 'tool') {
      // Move entire tool
      const updatedPoints = drawing.points.map((point: any) => {
        const pixelCoords = chartToPixelCoords(point.time, point.price);
        if (!pixelCoords) return point;

        const newPixelCoords = {
          x: pixelCoords.x + deltaX,
          y: pixelCoords.y + deltaY
        };

        const newChartCoords = pixelToChartCoords(newPixelCoords.x, newPixelCoords.y);
        return newChartCoords || point;
      });

      const updatedDrawing = {
        ...drawing,
        points: updatedPoints
      };

      onUpdateDrawing(dragState.drawingIndex, updatedDrawing);
    }
  }, [dragState, drawings, pixelToChartCoords, chartToPixelCoords, onUpdateDrawing]);

  // Create draggable handles for a drawing
  const createDraggableHandles = useCallback((drawing: any, drawingIndex: number) => {
    if (!drawing.points) return [];

    const handles: any[] = [];

    drawing.points.forEach((point: any, pointIndex: number) => {
      const timeValue = typeof point.time === 'string' ? point.time : new Date(point.time).toISOString();
      
      handles.push({
        name: `Handle_${drawingIndex}_${pointIndex}`,
        type: 'scatter',
        coordinateSystem: 'cartesian2d',
        data: [[timeValue, point.price]],
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: drawing.style?.color || '#00e7b6',
          borderColor: '#ffffff',
          borderWidth: 2,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
          shadowBlur: 4
        },
        z: 2000,
        silent: false,
        animation: false,
        cursor: 'move',
        drawingIndex,
        pointIndex,
        isDraggableHandle: true
      });
    });

    return handles;
  }, []);

  // Create invisible hit area for tool selection
  const createToolHitArea = useCallback((drawing: any, drawingIndex: number) => {
    if (!drawing.points || drawing.points.length < 2) return null;

    const hitAreaData = drawing.points.map((point: any) => {
      const timeValue = typeof point.time === 'string' ? point.time : new Date(point.time).toISOString();
      return [timeValue, point.price];
    });

    return {
      name: `HitArea_${drawingIndex}`,
      type: 'line',
      coordinateSystem: 'cartesian2d',
      data: hitAreaData,
      lineStyle: {
        color: 'transparent',
        width: 15 // Wide invisible line for easier selection
      },
      symbol: 'none',
      z: 1999,
      silent: false,
      animation: false,
      cursor: 'move',
      drawingIndex,
      isToolHitArea: true
    };
  }, []);

  return {
    dragState,
    startDrag,
    createDraggableHandles,
    createToolHitArea,
    pixelToChartCoords,
    chartToPixelCoords
  };
};

export default useDraggableTools;
