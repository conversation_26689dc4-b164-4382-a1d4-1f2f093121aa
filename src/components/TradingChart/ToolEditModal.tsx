import React, { useState, useEffect } from 'react';
import { X, Palette, Move, RotateCcw } from 'lucide-react';

interface ToolEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  drawing: any;
  onUpdate: (updatedDrawing: any) => void;
  onDelete: () => void;
}

const ToolEditModal: React.FC<ToolEditModalProps> = ({
  isOpen,
  onClose,
  drawing,
  onUpdate,
  onDelete
}) => {
  const [localDrawing, setLocalDrawing] = useState(drawing);
  const [activeTab, setActiveTab] = useState<'style' | 'position'>('style');

  useEffect(() => {
    setLocalDrawing(drawing);
  }, [drawing]);

  if (!isOpen || !drawing) return null;

  const handleColorChange = (property: string, color: string) => {
    const updated = {
      ...localDrawing,
      style: {
        ...localDrawing.style,
        [property]: color
      }
    };
    setLocalDrawing(updated);
    onUpdate(updated);
  };

  const handleStyleChange = (property: string, value: any) => {
    const updated = {
      ...localDrawing,
      style: {
        ...localDrawing.style,
        [property]: value
      }
    };
    setLocalDrawing(updated);
    onUpdate(updated);
  };

  const handlePositionChange = (pointIndex: number, axis: 'time' | 'price', value: number) => {
    const updated = {
      ...localDrawing,
      points: localDrawing.points.map((point: any, index: number) => {
        if (index === pointIndex) {
          return {
            ...point,
            [axis]: axis === 'time' ? new Date(value).toISOString() : value
          };
        }
        return point;
      })
    };
    setLocalDrawing(updated);
    onUpdate(updated);
  };

  const getToolSpecificOptions = () => {
    switch (drawing.type) {
      case 'line':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Line Color</label>
              <input
                type="color"
                value={localDrawing.style?.color || '#00e7b6'}
                onChange={(e) => handleColorChange('color', e.target.value)}
                className="w-full h-10 rounded-lg border border-white/[0.12] bg-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Line Width</label>
              <input
                type="range"
                min="1"
                max="10"
                value={localDrawing.style?.width || 2}
                onChange={(e) => handleStyleChange('width', parseInt(e.target.value))}
                className="w-full"
              />
              <span className="text-xs text-white/60">{localDrawing.style?.width || 2}px</span>
            </div>
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Line Style</label>
              <select
                value={localDrawing.style?.type || 'solid'}
                onChange={(e) => handleStyleChange('type', e.target.value)}
                className="w-full p-2 rounded-lg border border-white/[0.12] bg-[#1a1a1a] text-white"
              >
                <option value="solid">Solid</option>
                <option value="dashed">Dashed</option>
                <option value="dotted">Dotted</option>
              </select>
            </div>
          </div>
        );

      case 'rr':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Entry Color</label>
              <input
                type="color"
                value={localDrawing.style?.entryColor || '#ffffff'}
                onChange={(e) => handleColorChange('entryColor', e.target.value)}
                className="w-full h-10 rounded-lg border border-white/[0.12] bg-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Stop Loss Color</label>
              <input
                type="color"
                value={localDrawing.style?.stopColor || '#ff4757'}
                onChange={(e) => handleColorChange('stopColor', e.target.value)}
                className="w-full h-10 rounded-lg border border-white/[0.12] bg-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Take Profit Color</label>
              <input
                type="color"
                value={localDrawing.style?.targetColor || '#00e7b6'}
                onChange={(e) => handleColorChange('targetColor', e.target.value)}
                className="w-full h-10 rounded-lg border border-white/[0.12] bg-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Line Width</label>
              <input
                type="range"
                min="1"
                max="5"
                value={localDrawing.style?.width || 2}
                onChange={(e) => handleStyleChange('width', parseInt(e.target.value))}
                className="w-full"
              />
              <span className="text-xs text-white/60">{localDrawing.style?.width || 2}px</span>
            </div>
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Show Labels</label>
              <input
                type="checkbox"
                checked={localDrawing.style?.showLabels !== false}
                onChange={(e) => handleStyleChange('showLabels', e.target.checked)}
                className="w-4 h-4"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Show Risk/Reward Ratio</label>
              <input
                type="checkbox"
                checked={localDrawing.style?.showRatio !== false}
                onChange={(e) => handleStyleChange('showRatio', e.target.checked)}
                className="w-4 h-4"
              />
            </div>
          </div>
        );

      case 'circle':
      case 'rectangle':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Border Color</label>
              <input
                type="color"
                value={localDrawing.style?.color || '#00e7b6'}
                onChange={(e) => handleColorChange('color', e.target.value)}
                className="w-full h-10 rounded-lg border border-white/[0.12] bg-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Fill Color</label>
              <input
                type="color"
                value={localDrawing.style?.fillColor || 'transparent'}
                onChange={(e) => handleColorChange('fillColor', e.target.value)}
                className="w-full h-10 rounded-lg border border-white/[0.12] bg-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Border Width</label>
              <input
                type="range"
                min="1"
                max="10"
                value={localDrawing.style?.width || 2}
                onChange={(e) => handleStyleChange('width', parseInt(e.target.value))}
                className="w-full"
              />
              <span className="text-xs text-white/60">{localDrawing.style?.width || 2}px</span>
            </div>
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Fill Opacity</label>
              <input
                type="range"
                min="0"
                max="100"
                value={(localDrawing.style?.fillOpacity || 0) * 100}
                onChange={(e) => handleStyleChange('fillOpacity', parseInt(e.target.value) / 100)}
                className="w-full"
              />
              <span className="text-xs text-white/60">{Math.round((localDrawing.style?.fillOpacity || 0) * 100)}%</span>
            </div>
          </div>
        );

      default:
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Color</label>
              <input
                type="color"
                value={localDrawing.style?.color || '#00e7b6'}
                onChange={(e) => handleColorChange('color', e.target.value)}
                className="w-full h-10 rounded-lg border border-white/[0.12] bg-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Width</label>
              <input
                type="range"
                min="1"
                max="10"
                value={localDrawing.style?.width || 2}
                onChange={(e) => handleStyleChange('width', parseInt(e.target.value))}
                className="w-full"
              />
              <span className="text-xs text-white/60">{localDrawing.style?.width || 2}px</span>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[9999]">
      <div className="bg-[#1a1a1a] border border-white/[0.12] rounded-xl p-6 w-96 max-h-[80vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-white">
            Edit {drawing.type.charAt(0).toUpperCase() + drawing.type.slice(1)} Tool
          </h3>
          <button
            onClick={onClose}
            className="text-white/60 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex mb-6 border-b border-white/[0.12]">
          <button
            onClick={() => setActiveTab('style')}
            className={`px-4 py-2 text-sm font-medium transition-colors ${
              activeTab === 'style'
                ? 'text-white border-b-2 border-[#00e7b6]'
                : 'text-white/60 hover:text-white'
            }`}
          >
            <Palette className="w-4 h-4 inline mr-2" />
            Style
          </button>
          <button
            onClick={() => setActiveTab('position')}
            className={`px-4 py-2 text-sm font-medium transition-colors ${
              activeTab === 'position'
                ? 'text-white border-b-2 border-[#00e7b6]'
                : 'text-white/60 hover:text-white'
            }`}
          >
            <Move className="w-4 h-4 inline mr-2" />
            Position
          </button>
        </div>

        {/* Content */}
        <div className="mb-6">
          {activeTab === 'style' ? (
            getToolSpecificOptions()
          ) : (
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-white/80">Adjust Points</h4>
              {localDrawing.points?.map((point: any, index: number) => (
                <div key={index} className="space-y-2 p-3 bg-white/[0.05] rounded-lg">
                  <h5 className="text-xs font-medium text-white/70">
                    Point {index + 1} {drawing.type === 'rr' && ['(Entry)', '(Stop)', '(Target)'][index]}
                  </h5>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="block text-xs text-white/60 mb-1">Price</label>
                      <input
                        type="number"
                        step="0.01"
                        value={point.price}
                        onChange={(e) => handlePositionChange(index, 'price', parseFloat(e.target.value))}
                        className="w-full p-2 text-xs rounded border border-white/[0.12] bg-[#0a0a0a] text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-white/60 mb-1">Time</label>
                      <input
                        type="datetime-local"
                        value={new Date(point.time).toISOString().slice(0, 16)}
                        onChange={(e) => handlePositionChange(index, 'time', new Date(e.target.value).getTime())}
                        className="w-full p-2 text-xs rounded border border-white/[0.12] bg-[#0a0a0a] text-white"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex gap-3">
          <button
            onClick={onDelete}
            className="flex-1 px-4 py-2 bg-red-600/20 border border-red-600/30 text-red-400 rounded-lg hover:bg-red-600/30 transition-colors text-sm font-medium"
          >
            Delete Tool
          </button>
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 bg-[#00e7b6]/20 border border-[#00e7b6]/30 text-[#00e7b6] rounded-lg hover:bg-[#00e7b6]/30 transition-colors text-sm font-medium"
          >
            Done
          </button>
        </div>
      </div>
    </div>
  );
};

export default ToolEditModal;
