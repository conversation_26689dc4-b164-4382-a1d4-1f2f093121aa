import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { runAgent, runCustomAgent } from '@/services/agentService';

interface AgentRunDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  agentId?: string;
  agentConfig?: any;
}

const AgentRunDialog: React.FC<AgentRunDialogProps> = ({
  open,
  onOpenChange,
  agentId,
  agentConfig
}) => {
  const [symbol, setSymbol] = useState<string>('AAPL');
  const [timeframe, setTimeframe] = useState<string>('day');
  const [isRunning, setIsRunning] = useState<boolean>(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Reset state when dialog opens/closes
  React.useEffect(() => {
    if (!open) {
      setResult(null);
      setError(null);
    }
  }, [open]);

  // Handle run
  const handleRun = async () => {
    // Validate inputs
    if (!symbol) {
      setError('Please enter a symbol');
      return;
    }

    // Validate agent
    if (!agentId && !agentConfig) {
      setError('No agent ID or configuration provided');
      return;
    }

    // Start running
    setIsRunning(true);
    setError(null);
    setResult(null);

    try {
      let runResult;

      if (agentId) {
        // Run saved agent
        runResult = await runAgent(agentId, symbol, timeframe);
      } else if (agentConfig) {
        // Run custom agent
        runResult = await runCustomAgent(agentConfig, symbol, timeframe);
      } else {
        throw new Error('No agent ID or configuration provided');
      }

      // Validate result
      if (!runResult) {
        throw new Error('No result returned from agent runner');
      }

      // Ensure the result has all required fields
      const formattedResult = {
        signal: runResult.signal || 'neutral',
        confidence: typeof runResult.confidence === 'number' ? runResult.confidence : 0,
        reasoning: runResult.reasoning || '',
        metrics: runResult.metrics || {},
        executionPath: runResult.executionPath || [],
        executionTime: runResult.executionTime || 0,
        timestamp: runResult.timestamp || new Date().toISOString(),
        debugLogs: runResult.debugLogs || []
      };

      setResult(formattedResult);
    } catch (err) {
      // Provide more specific error messages
      if (err instanceof Error) {
        // Format the error message for better readability
        const errorMessage = err.message
          .replace('Agent runner error:', '')
          .replace('Failed to run agent:', '')
          .trim();

        if (err.message.includes('indicator') || err.message.includes('calculation') ||
            err.message.includes('RSI') || err.message.includes('MACD') ||
            err.message.includes('technical')) {
          setError('Indicator calculation error: There was an issue calculating technical indicators. Please check your agent configuration and ensure all indicator blocks are properly configured.');
        } else if (err.message.includes('Edge Function returned a non-2xx status code')) {
          setError('The agent runner service returned an error (HTTP 400). This could be due to invalid agent configuration or missing required parameters.');
        } else if (err.message.includes('User not authenticated')) {
          setError('Authentication error: You must be logged in to run agents. Please log in and try again.');
        } else if (err.message.includes('Invalid request')) {
          setError(`Invalid request: ${errorMessage}. Please check your agent configuration.`);
        } else if (err.message.includes('Server error')) {
          setError(`Server error: ${errorMessage}. Please try again later or contact support if the issue persists.`);
        } else if (err.message.includes('Invalid response format')) {
          setError('The agent runner returned an invalid response format. This may indicate an issue with the agent execution.');
        } else if (err.message.includes('data') || err.message.includes('historical')) {
          setError('Data error: Unable to fetch historical data for the specified symbol. Please try a different symbol or timeframe.');
        } else {
          setError(errorMessage || 'An error occurred while running the agent');
        }
      } else {
        setError('An unknown error occurred while running the agent');
      }
    } finally {
      setIsRunning(false);
    }
  };

  // Render signal badge
  const renderSignalBadge = (signal: string) => {
    switch (signal) {
      case 'bullish':
        return (
          <Badge className="bg-green-500">
            <TrendingUp className="h-3 w-3 mr-1" />
            Bullish
          </Badge>
        );
      case 'bearish':
        return (
          <Badge className="bg-red-500">
            <TrendingDown className="h-3 w-3 mr-1" />
            Bearish
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            <Minus className="h-3 w-3 mr-1" />
            Neutral
          </Badge>
        );
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Run Agent</DialogTitle>
          <DialogDescription>
            Run your agent on a specific symbol and timeframe.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="symbol" className="text-sm font-medium mb-1 block">
                Symbol
              </label>
              <Input
                id="symbol"
                value={symbol}
                onChange={e => setSymbol(e.target.value.toUpperCase())}
                placeholder="e.g., AAPL"
                disabled={isRunning}
              />
            </div>
            <div>
              <label htmlFor="timeframe" className="text-sm font-medium mb-1 block">
                Timeframe
              </label>
              <Select
                value={timeframe}
                onValueChange={setTimeframe}
                disabled={isRunning}
              >
                <SelectTrigger id="timeframe">
                  <SelectValue placeholder="Select timeframe" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="day">Daily</SelectItem>
                  <SelectItem value="hour">Hourly</SelectItem>
                  <SelectItem value="15minute">15 Minutes</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3 text-red-800">
              <div className="font-medium mb-1">Error</div>
              <div className="text-sm">{error}</div>
              <div className="mt-2 text-xs text-red-600">
                If this error persists, please check your agent configuration and try again.
                For technical issues, please contact support.
              </div>
            </div>
          )}

          {result && (
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Result</h3>
                    <div className="flex items-center gap-2">
                      {renderSignalBadge(result.signal)}
                      <Badge variant="outline">{result.confidence}% Confidence</Badge>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-1">Reasoning</h4>
                    <p className="text-sm text-muted-foreground">{result.reasoning}</p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-1">Metrics</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {result.metrics.indicators && Object.entries(result.metrics.indicators).map(([key, value]) => (
                        <div key={key} className="text-sm">
                          <span className="font-medium">{key}:</span>{' '}
                          {typeof value === 'object' ? JSON.stringify(value) : value}
                        </div>
                      ))}

                      {result.metrics.candlePatterns && Object.entries(result.metrics.candlePatterns).map(([key, value]) => (
                        <div key={key} className="text-sm col-span-2">
                          <span className="font-medium">{key}:</span>{' '}
                          {typeof value === 'object' && value !== null ? (
                            <div className="mt-1 space-y-1">
                              <div className="inline-flex items-center gap-2">
                                <span className={`px-2 py-1 rounded text-xs ${
                                  (value as any).detected
                                    ? (value as any).type === 'bullish'
                                      ? 'bg-green-100 text-green-800'
                                      : (value as any).type === 'bearish'
                                      ? 'bg-red-100 text-red-800'
                                      : 'bg-blue-100 text-blue-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {(value as any).detected ? ((value as any).type ? `${(value as any).type} detected` : 'detected') : 'not detected'}
                                </span>
                                <span className="text-xs text-muted-foreground">{((value as any).timeframe) ? `(${(value as any).timeframe})` : ''}</span>
                              </div>
                              {(value as any).primaryPattern && (
                                <div className="text-xs text-muted-foreground">
                                  Primary: <span className="font-medium">{(value as any).primaryPattern}</span>
                                </div>
                              )}
                              {(value as any).allPatterns && (value as any).allPatterns.length > 0 && (
                                <div className="text-xs text-muted-foreground">
                                  All patterns: {(value as any).allPatterns.map((p: any) => (
                                    <span key={p.name} className={`inline-block px-1 py-0.5 rounded mr-1 ${
                                      p.type === 'bullish' ? 'bg-green-50 text-green-700' :
                                      p.type === 'bearish' ? 'bg-red-50 text-red-700' :
                                      'bg-gray-50 text-gray-700'
                                    }`}>
                                      {p.name}
                                    </span>
                                  )).reduce((prev: any, curr: any) => [prev, ' ', curr]) as any}
                                </div>
                              )}
                            </div>
                          ) : (
                            JSON.stringify(value)
                          )}
                        </div>
                      ))}

                      {result.metrics.prices && Object.entries(result.metrics.prices).map(([key, value]) => (
                        <div key={key} className="text-sm">
                          <span className="font-medium">{key}:</span> {value}
                        </div>
                      ))}

                      {result.metrics.fundamentals && Object.entries(result.metrics.fundamentals).map(([key, value]) => (
                        <div key={key} className="text-sm">
                          <span className="font-medium">{key}:</span>{' '}
                          {typeof value === 'object' && value !== null && 'value' in value && 'components' in value ? (
                            <div className="mt-1">
                              <div className="font-medium text-green-600">
                                {typeof value.value === 'number' ? value.value.toFixed(2) : value.value}
                                {key === 'return_on_equity' ? '%' : ''}
                              </div>
                              {value.components && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  {key === 'return_on_equity' && value.components && (
                                    <>
                                      {(value.components as any).net_income_ttm && (
                                        <div>Net Income (TTM): ${(value.components as any).net_income_ttm.toLocaleString()}</div>
                                      )}
                                      {(value.components as any).shareholders_equity && (
                                        <div>Shareholders' Equity: ${(value.components as any).shareholders_equity.toLocaleString()}</div>
                                      )}
                                      {(value.components as any).period_note && (
                                        <div className="text-blue-600 font-medium">{(value.components as any).period_note}</div>
                                      )}
                                    </>
                                  )}
                                </div>
                              )}
                            </div>
                          ) : (
                            typeof value === 'object' ? JSON.stringify(value) : value
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-1">Execution Time</h4>
                    <p className="text-sm text-muted-foreground">{result.executionTime}ms</p>
                  </div>

                  {result.debugLogs && result.debugLogs.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Debug Logs</h4>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {result.debugLogs.map((log, index) => (
                          <div key={index} className="bg-gray-50 border rounded-md p-3 text-xs">
                            <div className="font-medium text-gray-900 mb-1">{log.message}</div>
                            {log.inputValue !== undefined && (
                              <div className="text-gray-600">
                                <span className="font-medium">Input Value:</span> {JSON.stringify(log.inputValue)}
                              </div>
                            )}
                            {log.currentPrice && (
                              <div className="text-gray-600">
                                <span className="font-medium">Current Price:</span> {log.currentPrice}
                              </div>
                            )}
                            {log.availableIndicators && log.availableIndicators.length > 0 && (
                              <div className="text-gray-600">
                                <span className="font-medium">Available Indicators:</span> {log.availableIndicators.join(', ')}
                              </div>
                            )}
                            <div className="text-gray-500 text-xs mt-1">
                              {new Date(log.timestamp).toLocaleTimeString()}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
          <Button onClick={handleRun} disabled={isRunning}>
            {isRunning ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Running...
              </>
            ) : (
              'Run Agent'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AgentRunDialog;
