import React from 'react';
import { useWhopAccess } from '@/contexts/WhopContext';
import { useWhopUser } from '@/contexts/WhopContext';

/**
 * Test component for the new experience and company fetch functionality
 * This component displays the fetched experience and company data
 */
export const WhopExperienceCompanyTest: React.FC = () => {
  const {
    isWhopUser,
    hasAccess,
    accessLevel,
    isAdmin,
    isCustomer,
    isLoading,
    experience,
    company,
    companyId,
    experienceLoading,
    companyLoading
  } = useWhopAccess();

  const { user: whopUser } = useWhopUser();

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Whop Experience & Company Data Test</h1>
      
      {/* User Access Info */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">User Access Information</h2>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <strong>Is Whop User:</strong> {isWhopUser ? 'Yes' : 'No'}
          </div>
          <div>
            <strong>Has Access:</strong> {hasAccess ? 'Yes' : 'No'}
          </div>
          <div>
            <strong>Access Level:</strong> {accessLevel}
          </div>
          <div>
            <strong>Is Admin:</strong> {isAdmin ? 'Yes' : 'No'}
          </div>
          <div>
            <strong>Is Customer:</strong> {isCustomer ? 'Yes' : 'No'}
          </div>
          <div>
            <strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}
          </div>
        </div>
      </div>

      {/* Experience Data */}
      <div className="mb-6 p-4 bg-green-50 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Experience Data</h2>
        <div className="mb-2">
          <strong>Loading:</strong> {experienceLoading ? 'Yes' : 'No'}
        </div>
        {experience ? (
          <div className="space-y-2 text-sm">
            <div>
              <strong>Experience ID:</strong> {experience.id}
            </div>
            <div>
              <strong>Name:</strong> {experience.name}
            </div>
            <div>
              <strong>Description:</strong> {experience.description || 'N/A'}
            </div>
            <div>
              <strong>Company ID (from experience):</strong> {experience.company?.id || 'N/A'}
            </div>
            <div>
              <strong>Company Title (from experience):</strong> {experience.company?.title || 'N/A'}
            </div>
            <div>
              <strong>App Name:</strong> {experience.app?.name || 'N/A'}
            </div>
            <details className="mt-2">
              <summary className="cursor-pointer font-medium">Raw Experience Data</summary>
              <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-40">
                {JSON.stringify(experience, null, 2)}
              </pre>
            </details>
          </div>
        ) : (
          <div className="text-gray-500">No experience data available</div>
        )}
      </div>

      {/* Company Data */}
      <div className="mb-6 p-4 bg-yellow-50 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Company Data</h2>
        <div className="mb-2">
          <strong>Company ID:</strong> {companyId || 'N/A'}
        </div>
        <div className="mb-2">
          <strong>Loading:</strong> {companyLoading ? 'Yes' : 'No'}
        </div>
        {company ? (
          <div className="space-y-2 text-sm">
            <div>
              <strong>Company ID:</strong> {company.id}
            </div>
            <div>
              <strong>Title:</strong> {company.title}
            </div>
            <div>
              <strong>Created At:</strong> {company.created_at ? new Date(company.created_at * 1000).toLocaleString() : 'N/A'}
            </div>
            <div>
              <strong>Route:</strong> {company.route || 'N/A'}
            </div>
            <div>
              <strong>Has Payment Method:</strong> {company.has_payment_method ? 'Yes' : 'No'}
            </div>
            <div>
              <strong>Owner Username:</strong> {company.owner?.username || 'N/A'}
            </div>
            <div>
              <strong>Owner Email:</strong> {company.owner?.email || 'N/A'}
            </div>
            <details className="mt-2">
              <summary className="cursor-pointer font-medium">Raw Company Data</summary>
              <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-40">
                {JSON.stringify(company, null, 2)}
              </pre>
            </details>
          </div>
        ) : (
          <div className="text-gray-500">
            {companyId ? 'Loading company data...' : 'No company data available'}
          </div>
        )}
      </div>

      {/* API Flow Status */}
      <div className="p-4 bg-purple-50 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">API Flow Status</h2>
        <div className="space-y-2 text-sm">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${experience ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <span>1. Experience Fetch {experience ? '✅' : '⏳'}</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${companyId ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <span>2. Company ID Extraction {companyId ? '✅' : '⏳'}</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${company ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <span>3. Company Fetch {company ? '✅' : '⏳'}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhopExperienceCompanyTest;
