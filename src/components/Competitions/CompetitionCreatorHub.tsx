import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Trophy, DollarSign, Users, Calendar, Globe, Lock, Award, Plus, Trash2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useCompetitions } from '@/hooks/useCompetitions';

interface CompetitionCreatorHubProps {
  isOpen: boolean;
  onClose: () => void;
  onCompetitionCreated?: () => void;
}

interface Prize {
  position: number;
  amount: number;
  percentage?: number;
}

interface CompetitionFormData {
  name: string;
  description: string;
  starting_balance: number;
  max_participants?: number;
  entry_fee: number;
  prize_pool: number;
  competition_start: string;
  competition_end: string;
  is_public: boolean;
  prizes: Prize[];
}

const CompetitionCreatorHub: React.FC<CompetitionCreatorHubProps> = ({
  isOpen,
  onClose,
  onCompetitionCreated
}) => {
  const { toast } = useToast();
  const { createCompetition } = useCompetitions();
  const [formData, setFormData] = useState<CompetitionFormData>({
    name: '',
    description: '',
    starting_balance: 100000,
    entry_fee: 0,
    prize_pool: 0,
    competition_start: '',
    competition_end: '',
    is_public: true,
    prizes: [
      { position: 1, amount: 0, percentage: 50 },
      { position: 2, amount: 0, percentage: 30 },
      { position: 3, amount: 0, percentage: 20 }
    ]
  });

  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);

  // Calculate prize amounts based on percentages
  const calculatePrizeAmounts = () => {
    const updatedPrizes = formData.prizes.map(prize => ({
      ...prize,
      amount: Math.round((formData.prize_pool * (prize.percentage || 0)) / 100)
    }));
    setFormData(prev => ({ ...prev, prizes: updatedPrizes }));
  };

  // Update prize amounts when prize pool changes
  React.useEffect(() => {
    if (formData.prize_pool > 0) {
      calculatePrizeAmounts();
    }
  }, [formData.prize_pool]);

  const handleSubmit = async () => {
    setLoading(true);

    try {
      // Create competition data in the format expected by the API
      const competitionData = {
        name: formData.name,
        description: formData.description,
        starting_balance: formData.starting_balance,
        max_participants: formData.max_participants,
        entry_fee: formData.entry_fee,
        prize_pool: formData.prize_pool,
        competition_start: formData.competition_start,
        competition_end: formData.competition_end,
        rules: {
          prizes: formData.prizes.filter(p => p.amount > 0),
          is_public: formData.is_public
        }
      };

      console.log('Creating competition with data:', competitionData);

      // Actually create the competition using the hook
      const result = await createCompetition(competitionData);
      console.log('Competition creation result:', result);

      if (result) {
        toast({
          title: "Competition Created!",
          description: `"${formData.name}" is now live. Go to "My Competitions" to manage and view analytics.`,
        });

        onCompetitionCreated?.();
        onClose();
      } else {
        throw new Error('Failed to create competition');
      }
    } catch (error) {
      console.error('Error creating competition:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create competition. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: keyof CompetitionFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const updatePrize = (index: number, field: keyof Prize, value: number) => {
    const updatedPrizes = [...formData.prizes];
    updatedPrizes[index] = { ...updatedPrizes[index], [field]: value };
    setFormData(prev => ({ ...prev, prizes: updatedPrizes }));
  };

  const addPrize = () => {
    const newPosition = formData.prizes.length + 1;
    setFormData(prev => ({
      ...prev,
      prizes: [...prev.prizes, { position: newPosition, amount: 0, percentage: 0 }]
    }));
  };

  const removePrize = (index: number) => {
    if (formData.prizes.length > 1) {
      const updatedPrizes = formData.prizes.filter((_, i) => i !== index);
      setFormData(prev => ({ ...prev, prizes: updatedPrizes }));
    }
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/70 backdrop-blur-md z-50 flex items-center justify-center p-4"
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 20 }}
        className="w-full max-w-2xl max-h-[90vh] overflow-hidden"
      >
        <div className="bg-[#0A0A0A] border border-white/[0.08] rounded-lg shadow-2xl backdrop-blur-xl">
          {/* Simple Header */}
          <div className="border-b border-white/[0.08] p-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-semibold text-white mb-2" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                  Create Competition
                </h1>
                <p className="text-white/60 text-sm" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                  Set up a new trading competition
                </p>
              </div>
              <button
                onClick={onClose}
                className="w-8 h-8 bg-white/[0.05] hover:bg-white/[0.1] rounded-lg flex items-center justify-center text-white/60 hover:text-white transition-all duration-200"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 max-h-[65vh] overflow-y-auto competition-scrollbar">
            <div className="space-y-6">
              {/* Competition Details */}
              <div className="space-y-4">
                <h2 className="text-lg font-medium text-white" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                  Competition Details
                </h2>

                <div className="space-y-4">
                  <div>
                    <label className="text-white text-sm font-medium block mb-2" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                      Competition Name
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => updateFormData('name', e.target.value)}
                      placeholder="e.g., Weekly Crypto Challenge"
                      className="w-full px-3 py-3 bg-white/[0.03] border border-white/[0.08] hover:border-white/[0.12] focus:border-white/[0.15] focus:outline-none rounded-lg text-white placeholder:text-white/40 transition-colors duration-200 text-sm"
                      style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                    />
                  </div>

                  <div>
                    <label className="text-white text-sm font-medium block mb-2" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                      Description
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => updateFormData('description', e.target.value)}
                      placeholder="Describe your competition rules and objectives..."
                      rows={3}
                      className="w-full px-3 py-3 bg-white/[0.03] border border-white/[0.08] hover:border-white/[0.12] focus:border-white/[0.15] focus:outline-none rounded-lg text-white placeholder:text-white/40 transition-colors duration-200 resize-none text-sm"
                      style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                    />
                  </div>

                </div>
              </div>

              {/* Financial Settings */}
              <div className="space-y-4">
                <h2 className="text-lg font-medium text-white" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                  Financial Settings
                </h2>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-white text-sm font-medium block mb-2" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                      Starting Balance ($)
                    </label>
                    <input
                      type="number"
                      value={formData.starting_balance}
                      onChange={(e) => updateFormData('starting_balance', Number(e.target.value))}
                      className="w-full px-3 py-3 bg-white/[0.03] border border-white/[0.08] hover:border-white/[0.12] focus:border-white/[0.15] focus:outline-none rounded-lg text-white transition-colors duration-200 text-sm"
                      style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                    />
                  </div>

                  <div>
                    <label className="text-white text-sm font-medium block mb-2" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                      Prize Pool ($)
                    </label>
                    <input
                      type="number"
                      value={formData.prize_pool}
                      onChange={(e) => updateFormData('prize_pool', Number(e.target.value))}
                      className="w-full px-3 py-3 bg-white/[0.03] border border-white/[0.08] hover:border-white/[0.12] focus:border-white/[0.15] focus:outline-none rounded-lg text-white transition-colors duration-200 text-sm"
                      style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                    />
                  </div>
                </div>
              </div>

              {/* Schedule */}
              <div className="space-y-4">
                <h2 className="text-lg font-medium text-white" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                  Schedule
                </h2>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-white text-sm font-medium block mb-2" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                      Start Date & Time
                    </label>
                    <input
                      type="datetime-local"
                      value={formData.competition_start}
                      onChange={(e) => updateFormData('competition_start', e.target.value)}
                      className="w-full px-3 py-3 bg-white/[0.03] border border-white/[0.08] hover:border-white/[0.12] focus:border-white/[0.15] focus:outline-none rounded-lg text-white transition-colors duration-200 text-sm [color-scheme:dark] font-mono"
                      style={{
                        fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                        fontVariantNumeric: 'tabular-nums'
                      }}
                    />
                  </div>

                  <div>
                    <label className="text-white text-sm font-medium block mb-2" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                      End Date & Time
                    </label>
                    <input
                      type="datetime-local"
                      value={formData.competition_end}
                      onChange={(e) => updateFormData('competition_end', e.target.value)}
                      className="w-full px-3 py-3 bg-white/[0.03] border border-white/[0.08] hover:border-white/[0.12] focus:border-white/[0.15] focus:outline-none rounded-lg text-white transition-colors duration-200 text-sm [color-scheme:dark] font-mono"
                      style={{
                        fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                        fontVariantNumeric: 'tabular-nums'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* Prize Distribution */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-medium text-white" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                    Prize Distribution
                  </h2>
                  <button
                    type="button"
                    onClick={addPrize}
                    className="bg-white/[0.05] hover:bg-white/[0.08] border border-white/[0.08] hover:border-white/[0.12] text-white px-3 py-1.5 rounded-lg text-xs font-medium transition-colors duration-200 flex items-center gap-1.5 shadow-[inset_0_1px_0_rgba(255,255,255,0.05)]"
                    style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                  >
                    <Plus className="w-3 h-3" />
                    Add Prize
                  </button>
                </div>

                  <div className="space-y-3">
                    {formData.prizes.map((prize, index) => (
                      <div key={index} className="flex items-center gap-3 p-3 bg-white/[0.02] border border-white/[0.08] rounded-lg">
                        <div className="flex items-center gap-2 min-w-0">
                          <Award className="w-4 h-4 text-yellow-400 flex-shrink-0" />
                          <span className="text-white text-sm font-medium">#{prize.position}</span>
                        </div>

                        <div className="flex-1 grid grid-cols-2 gap-2">
                          <div>
                            <input
                              type="number"
                              value={prize.percentage || ''}
                              onChange={(e) => updatePrize(index, 'percentage', Number(e.target.value))}
                              placeholder="Percentage"
                              className="w-full px-3 py-2 bg-white/[0.05] border border-white/[0.08] rounded-lg text-white text-sm placeholder:text-white/40 focus:border-green-500/50 focus:outline-none transition-all duration-200"
                              style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                            />
                          </div>
                          <div className="flex items-center">
                            <span className="text-white/60 text-sm">
                              ${prize.amount.toLocaleString()}
                            </span>
                          </div>
                        </div>

                        {formData.prizes.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removePrize(index)}
                            className="text-white/40 hover:text-red-400 p-1 rounded transition-colors duration-200"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    ))}
                  </div>

                  <div className="text-xs text-white/60">
                    Total: {formData.prizes.reduce((sum, p) => sum + (p.percentage || 0), 0)}%
                    (${formData.prizes.reduce((sum, p) => sum + p.amount, 0).toLocaleString()})
                  </div>
                </div>
              </div>

              {/* Privacy Toggle */}
              <div className="flex items-center justify-between p-4 bg-white/[0.02] border border-white/[0.08] rounded-lg">
                  <div className="flex items-center gap-3">
                    {formData.is_public ? (
                      <Globe className="w-5 h-5 text-green-400" />
                    ) : (
                      <Lock className="w-5 h-5 text-orange-400" />
                    )}
                    <div>
                      <h4 className="text-white font-medium text-sm">
                        {formData.is_public ? 'Public Competition' : 'Private Competition'}
                      </h4>
                      <p className="text-white/60 text-xs">
                        {formData.is_public
                          ? 'Anyone can discover and join'
                          : 'Invite-only competition'
                        }
                      </p>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => updateFormData('is_public', !formData.is_public)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-lg transition-all duration-200 shadow-[inset_0_1px_0_rgba(255,255,255,0.05)] ${
                      formData.is_public ? 'bg-green-600' : 'bg-white/20'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 shadow-sm ${
                        formData.is_public ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>
          {/* Footer */}
          <div className="border-t border-white/[0.08] p-6">
            <div className="flex items-center justify-between">
              <p className="text-white/60 text-sm" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                Complete all required fields to create your competition
              </p>

              <div className="flex items-center gap-3">
                <button
                  onClick={onClose}
                  className="px-3 py-2 text-white/70 hover:text-white hover:bg-white/[0.05] rounded-lg transition-colors duration-200 text-sm font-medium"
                  style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                >
                  Cancel
                </button>

                <button
                  onClick={handleSubmit}
                  disabled={loading || !formData.name || !formData.competition_start || !formData.competition_end}
                  className="px-4 py-2 bg-white/[0.08] hover:bg-white/[0.12] border border-white/[0.15] text-white disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors duration-200 text-sm font-medium shadow-[inset_0_1px_0_rgba(255,255,255,0.1),inset_0_-1px_0_rgba(0,0,0,0.1)]"
                  style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                >
                  {loading ? 'Creating...' : 'Create Competition'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default CompetitionCreatorHub;
