import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Trophy, TrendingUp, Users, DollarSign, Calendar, ArrowLeft, Eye, Target, Award, Crown } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useCompetitions, Competition } from '@/hooks/useCompetitions';
import { useAuth } from '@/contexts/AuthContext';
import { useProductionLockProps } from '@/hooks/useProductionLock';

interface CompetitionAnalyticsProps {
  onBack: () => void;
}

const CompetitionAnalytics: React.FC<CompetitionAnalyticsProps> = ({ onBack }) => {
  const { user } = useAuth();
  const { competitions, userCompetitions } = useCompetitions();
  const [selectedCompetition, setSelectedCompetition] = useState<Competition | null>(null);

  // Production lock props for create competition feature
  const createCompetitionLockProps = useProductionLockProps('create-competition');

  // Sam<PERSON> created competitions for analytics demo
  const sampleCreatedCompetitions = [
    {
      id: 'created-1',
      name: 'My Crypto Challenge',
      entry_fee: 50,
      participant_count: 127,
      status: 'active',
      prize_pool: 5000,
      revenue: 6350
    },
    {
      id: 'created-2',
      name: 'Stock Picking Contest',
      entry_fee: 100,
      participant_count: 89,
      status: 'completed',
      prize_pool: 8000,
      revenue: 8900
    },
    {
      id: 'created-3',
      name: 'Options Mastery',
      entry_fee: 200,
      participant_count: 45,
      status: 'active',
      prize_pool: 8000,
      revenue: 9000
    },
    {
      id: 'created-4',
      name: 'Forex Championship',
      entry_fee: 75,
      participant_count: 156,
      status: 'completed',
      prize_pool: 10000,
      revenue: 11700
    }
  ];

  // Get user's created competitions (use sample data for demo)
  const myCreatedCompetitions = competitions.filter(comp => comp.creator_id === user?.id);
  const allCreatedCompetitions = myCreatedCompetitions.length > 0 ? myCreatedCompetitions : sampleCreatedCompetitions;

  // Calculate analytics with enhanced sample data
  const totalRevenue = allCreatedCompetitions.reduce((sum, comp) => sum + ((comp as any).revenue || (comp.entry_fee || 0) * (comp.participant_count || 0)), 0);
  const totalParticipants = allCreatedCompetitions.reduce((sum, comp) => sum + (comp.participant_count || 0), 0);
  const activeCompetitions = allCreatedCompetitions.filter(comp => comp.status === 'active').length;
  const completedCompetitions = allCreatedCompetitions.filter(comp => comp.status === 'completed').length;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'active': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'completed': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -30 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className="min-h-screen bg-[#0A0A0A] text-white relative overflow-y-auto competition-scrollbar"
      style={{ height: 'auto' }}
    >
      {/* Subtle radial glow background */}
      <div
        className="absolute inset-0 pointer-events-none"
        style={{
          background: 'radial-gradient(circle at center, rgba(255,255,255,0.025) 0%, rgba(255,255,255,0.012) 35%, rgba(255,255,255,0.004) 60%, transparent 80%)',
          filter: 'blur(0.5px)'
        }}
      />
      
      {/* Enhanced Top Navbar */}
      <div className="w-full bg-[#0B0B0B] border-b border-white/[0.05] px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <img
              src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/logos/ChatGPT%20Image%20Jul%2010,%202025,%2006_41_45%20PM.png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJsb2dvcy9DaGF0R1BUIEltYWdlIEp1bCAxMCwgMjAyNSwgMDZfNDFfNDUgUE0ucG5nIiwiaWF0IjoxNzUzNTA1MDI4LCJleHAiOjE3ODUwNDEwMjh9.JHAAqCN14J2Qt0-rPdvvOvPMrX4VnMHTfr3FKXIFWjM"
              alt="TradeOff Logo"
              className="w-10 h-10"
            />
          </div>

          <div className="flex items-center gap-8">
            {/* Navigation - Top Right */}
            <div className="flex items-center gap-6">
              <button
                onClick={() => {
                  // Go back to chart (close all competition modals)
                  onBack();
                }}
                className="flex items-center gap-2 text-white/60 hover:text-white transition-colors group"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/tradeoff//home.png"
                  alt="Chart"
                  className="w-4 h-4 opacity-60 group-hover:opacity-100 transition-opacity"
                  style={{ filter: 'brightness(0) saturate(100%) invert(100%)' }}
                />
                <span className="text-sm">Chart</span>
              </button>
              <button
                onClick={() => {
                  // Go back to competitions dashboard
                  onBack();
                }}
                className="flex items-center gap-2 text-white/60 hover:text-white transition-colors group"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/tradeoff//Globe%20(1).png"
                  alt="Competitions"
                  className="w-4 h-4 opacity-60 group-hover:opacity-100 transition-opacity"
                  style={{ filter: 'brightness(0) saturate(100%) invert(100%)' }}
                />
                <span className="text-sm">Competitions</span>
              </button>
              <button
                className="flex items-center gap-2 text-white font-medium hover:text-white/80 transition-colors"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/tradeoff//Bar%20Chart%201.png"
                  alt="Analytics"
                  className="w-4 h-4 opacity-100"
                  style={{ filter: 'brightness(0) saturate(100%) invert(100%)' }}
                />
                <span className="text-sm">Analytics</span>
              </button>
            </div>

            {/* Create Competition Button - Gold Premium Style */}
            <button
              onClick={createCompetitionLockProps.disabled ? createCompetitionLockProps.onClick : undefined}
              className="flex items-center gap-3 bg-gradient-to-b from-[#FFD700] to-[#FFA500] hover:from-[#FFED4E] hover:to-[#FFD700] border-2 border-[#FFA500]/[0.6] hover:border-[#FFD700]/[0.8] text-black font-semibold px-4 py-2.5 rounded-lg text-sm transition-all duration-250 backdrop-blur-sm active:scale-[0.97] active:translate-y-[1px] relative overflow-hidden group"
              style={{
                fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                boxShadow: `
                  0 0 20px rgba(255, 215, 0, 0.3),
                  0 4px 12px rgba(0, 0, 0, 0.2),
                  inset 0 1px 0 rgba(255, 255, 255, 0.4),
                  inset 0 -1px 0 rgba(0, 0, 0, 0.1)
                `,
                ...createCompetitionLockProps.style
              }}
              title="Create Competition"
              disabled={createCompetitionLockProps.disabled}
            >
              {/* Subtle shine effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/[0.1] to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-700 ease-out" />

              <span className="relative z-10">Create Competition</span>
              <div className="flex items-center gap-1 relative z-10">
                <kbd className="px-2 py-1 text-xs font-semibold text-black/70 bg-white/[0.2] border border-white/[0.3] rounded shadow-sm min-w-[20px] h-[20px] flex items-center justify-center">
                  ⌘
                </kbd>
                <kbd className="px-2 py-1 text-xs font-semibold text-black/70 bg-white/[0.2] border border-white/[0.3] rounded shadow-sm min-w-[20px] h-[20px] flex items-center justify-center">
                  K
                </kbd>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8 pb-24 relative z-10">
        {/* Enhanced Page Title */}
        <div className="mb-8">
          <h1 className="text-3xl font-semibold text-white mb-3" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
            Competition Analytics
          </h1>
          <p className="text-white/60 text-base font-medium">
            Track your competition performance and revenue insights
          </p>
        </div>

        {/* Clean Metric Cards - Stripe Style */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8 sm:mb-12">
          {/* Total Revenue */}
          <Card className="bg-white/[0.01] border-white/[0.06] hover:border-white/[0.08] transition-all duration-200 shadow-[0_1px_3px_rgba(0,0,0,0.12)]">
            <CardContent className="p-6">
              <div className="mb-4">
                <div className="text-white/50 text-sm font-medium mb-2" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
                  Total Revenue
                </div>
                <div className="text-3xl font-medium text-white" style={{ fontFamily: 'Inter, system-ui, sans-serif', fontWeight: 500 }}>
                  ${totalRevenue.toLocaleString()}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="px-2 py-0.5 bg-emerald-500/[0.1] rounded-md">
                  <span className="text-xs text-emerald-400 font-medium">+12.5%</span>
                </div>
                <span className="text-xs text-white/40">vs last week</span>
              </div>
            </CardContent>
          </Card>

          {/* Total Participants */}
          <Card className="bg-white/[0.01] border-white/[0.06] hover:border-white/[0.08] transition-all duration-200 shadow-[0_1px_3px_rgba(0,0,0,0.12)]">
            <CardContent className="p-6">
              <div className="mb-4">
                <div className="text-white/50 text-sm font-medium mb-2" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
                  Total Participants
                </div>
                <div className="text-3xl font-medium text-white" style={{ fontFamily: 'Inter, system-ui, sans-serif', fontWeight: 500 }}>
                  {totalParticipants.toLocaleString()}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="px-2 py-0.5 bg-emerald-500/[0.1] rounded-md">
                  <span className="text-xs text-emerald-400 font-medium">+8.2%</span>
                </div>
                <span className="text-xs text-white/40">24h growth</span>
              </div>
            </CardContent>
          </Card>

          {/* Active Competitions */}
          <Card className="bg-white/[0.01] border-white/[0.06] hover:border-white/[0.08] transition-all duration-200 shadow-[0_1px_3px_rgba(0,0,0,0.12)]">
            <CardContent className="p-6">
              <div className="mb-4">
                <div className="text-white/50 text-sm font-medium mb-2" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
                  Active Competitions
                </div>
                <div className="text-3xl font-medium text-white" style={{ fontFamily: 'Inter, system-ui, sans-serif', fontWeight: 500 }}>
                  {activeCompetitions}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs text-amber-400 font-medium">Live now</span>
              </div>
            </CardContent>
          </Card>

          {/* Completed Competitions */}
          <Card className="bg-white/[0.01] border-white/[0.06] hover:border-white/[0.08] transition-all duration-200 shadow-[0_1px_3px_rgba(0,0,0,0.12)]">
            <CardContent className="p-6">
              <div className="mb-4">
                <div className="text-white/50 text-sm font-medium mb-2" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
                  Completed
                </div>
                <div className="text-3xl font-medium text-white" style={{ fontFamily: 'Inter, system-ui, sans-serif', fontWeight: 500 }}>
                  {completedCompetitions}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-white/30 rounded-full"></div>
                <span className="text-xs text-white/40 font-medium">All time</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Clean Revenue Bar Chart - Stripe Style */}
        <Card className="bg-white/[0.01] border-white/[0.06] hover:border-white/[0.08] transition-all duration-200 shadow-[0_1px_3px_rgba(0,0,0,0.12)] mb-8">
          <CardHeader className="pb-6 border-b border-white/[0.06]">
            <div className="flex items-center justify-between">
              <CardTitle className="text-white text-xl font-medium flex items-center gap-3" style={{ fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif', fontWeight: 500 }}>
                Revenue
              </CardTitle>
              <div className="flex items-center gap-3">
                <div className="px-2.5 py-1 bg-emerald-500/[0.1] border border-emerald-500/[0.2] rounded-md">
                  <span className="text-xs text-emerald-400 font-medium">+18.5%</span>
                </div>
                <span className="text-xs text-white/40 font-medium">vs last week</span>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-4 sm:p-6 lg:p-8">
            <div className="h-64 sm:h-80 lg:h-96 flex items-end justify-between gap-1 sm:gap-2 relative">
              {/* Subtle grid lines */}
              <div className="absolute inset-0 flex flex-col justify-between pointer-events-none">
                {[0, 1, 2, 3, 4].map((line) => (
                  <div key={line} className="w-full h-px bg-white/[0.03]" />
                ))}
              </div>

              {/* Taller, thinner vertical bars */}
              {[
                { value: 1250, date: 'Dec 18', day: 'Mon' },
                { value: 890, date: 'Dec 19', day: 'Tue' },
                { value: 2100, date: 'Dec 20', day: 'Wed' },
                { value: 1680, date: 'Dec 21', day: 'Thu' },
                { value: 3200, date: 'Dec 22', day: 'Fri' },
                { value: 2850, date: 'Dec 23', day: 'Sat' },
                { value: 1950, date: 'Dec 24', day: 'Sun' },
                { value: 2750, date: 'Dec 25', day: 'Mon' },
                { value: 3100, date: 'Dec 26', day: 'Tue' },
                { value: 2400, date: 'Dec 27', day: 'Wed' },
                { value: 3850, date: 'Dec 28', day: 'Thu' },
                { value: 4200, date: 'Dec 29', day: 'Fri' },
                { value: 3650, date: 'Dec 30', day: 'Sat' },
                { value: 2900, date: 'Dec 31', day: 'Sun' }
              ].map((data, index) => (
                <div key={index} className="flex-1 flex flex-col items-center group relative">
                  {/* Clean tooltip */}
                  <div className="absolute -top-16 left-1/2 transform -translate-x-1/2 bg-[#1A1A1A] border border-white/[0.1] rounded-lg px-3 py-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 shadow-xl">
                    <div className="text-white text-sm font-medium">${data.value.toLocaleString()}</div>
                    <div className="text-white/50 text-xs">{data.date}</div>
                  </div>

                  {/* Thinner vertical bar with gold fill */}
                  <div
                    className="w-3 bg-gradient-to-t from-[#FFD700]/[0.15] to-[#FFD700]/[0.25] hover:from-[#FFD700]/[0.25] hover:to-[#FFD700]/[0.4] rounded-t-sm transition-all duration-300 relative overflow-hidden border-t border-[#FFD700]/[0.3]"
                    style={{
                      height: `${(data.value / 4200) * 100}%`,
                      minHeight: '12px',
                      boxShadow: '0 0 8px rgba(255, 215, 0, 0.2)'
                    }}
                  >
                    {/* Gold highlight on hover */}
                    <div className="absolute inset-0 bg-gradient-to-t from-transparent to-[#FFD700]/[0.1] opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>

                  {/* Minimal date labels */}
                  <div className="mt-3 text-center">
                    <div className="text-xs text-white/40 font-medium" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>{data.date.split(' ')[1]}</div>
                  </div>
                </div>
              ))}
            </div>

            {/* Clean summary stats */}
            <div className="mt-8 pt-6 border-t border-white/[0.06] grid grid-cols-3 gap-8">
              <div>
                <div className="text-white/40 text-sm font-medium mb-1" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>Peak Day</div>
                <div className="text-white text-lg font-medium">$4,200</div>
              </div>
              <div>
                <div className="text-white/40 text-sm font-medium mb-1" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>Average</div>
                <div className="text-white text-lg font-medium">$2,650</div>
              </div>
              <div>
                <div className="text-white/40 text-sm font-medium mb-1" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>Total Period</div>
                <div className="text-white text-lg font-medium">$37,100</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Competitions I'm Participating In */}
        <Card className="bg-white/[0.01] border-white/[0.06] hover:border-white/[0.08] transition-all duration-200 shadow-[0_1px_3px_rgba(0,0,0,0.12)] mb-8">
          <CardHeader className="pb-6 border-b border-white/[0.06]">
            <CardTitle className="text-white text-xl font-medium" style={{ fontFamily: 'Inter, system-ui, sans-serif', fontWeight: 500 }}>
              My Active Competitions
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="space-y-0">
              {/* Sample user competitions */}
              {[
                {
                  id: 'user-comp-1',
                  name: 'Weekly Crypto Challenge',
                  status: 'active',
                  entry_fee: 50,
                  participant_count: 127,
                  prize_pool: 5000,
                  my_rank: 15,
                  my_return: '+12.3%',
                  time_remaining: '3 days',
                  competition_start: new Date('2025-01-01'),
                  competition_end: new Date('2025-01-08')
                },
                {
                  id: 'user-comp-2',
                  name: 'S&P 500 Masters',
                  status: 'active',
                  entry_fee: 100,
                  participant_count: 89,
                  prize_pool: 10000,
                  my_rank: 7,
                  my_return: '+18.7%',
                  time_remaining: '12 days',
                  competition_start: new Date('2025-01-01'),
                  competition_end: new Date('2025-01-31')
                },
                {
                  id: 'user-comp-3',
                  name: 'Forex Fury',
                  status: 'completed',
                  entry_fee: 75,
                  participant_count: 156,
                  prize_pool: 7500,
                  my_rank: 23,
                  my_return: '+8.4%',
                  time_remaining: 'Ended',
                  competition_start: new Date('2024-12-15'),
                  competition_end: new Date('2024-12-29')
                }
              ].map((competition, index) => (
                <div
                  key={competition.id}
                  className={`p-6 hover:bg-white/[0.03] transition-all duration-200 cursor-pointer group ${
                    index !== 2 ? 'border-b border-white/[0.05]' : ''
                  }`}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-white font-semibold text-base group-hover:text-white/80 transition-colors" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                          {competition.name}
                        </h3>
                        <div className={`px-2 py-1 rounded-md text-xs font-medium ${
                          competition.status === 'active'
                            ? 'bg-[#22C55E]/[0.15] border border-[#22C55E]/[0.3] text-[#22C55E]'
                            : 'bg-white/[0.08] border border-white/[0.15] text-white/60'
                        }`}>
                          {competition.status}
                        </div>
                        {competition.my_rank <= 10 && competition.status === 'active' && (
                          <div className="px-2 py-1 rounded-md text-xs font-medium bg-amber-500/[0.15] border border-amber-500/[0.3] text-amber-400">
                            Top 10
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="text-right ml-4">
                      <div className="text-xl font-semibold text-white mb-1" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                        #{competition.my_rank}
                      </div>
                      <div className="text-xs text-white/50 font-medium">My Rank</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-4 gap-4">
                    <div className="bg-white/[0.03] rounded-lg p-3 text-center">
                      <div className={`font-semibold text-sm mb-1 ${
                        competition.my_return.startsWith('+') ? 'text-emerald-400' : 'text-red-400'
                      }`}>
                        {competition.my_return}
                      </div>
                      <div className="text-white/50 text-xs">My Return</div>
                    </div>
                    <div className="bg-white/[0.03] rounded-lg p-3 text-center">
                      <div className="text-white font-semibold text-sm mb-1">{competition.participant_count}</div>
                      <div className="text-white/50 text-xs">Participants</div>
                    </div>
                    <div className="bg-white/[0.03] rounded-lg p-3 text-center">
                      <div className="text-white font-semibold text-sm mb-1">${competition.prize_pool.toLocaleString()}</div>
                      <div className="text-white/50 text-xs">Prize Pool</div>
                    </div>
                    <div className="bg-white/[0.03] rounded-lg p-3 text-center">
                      <div className={`font-semibold text-sm mb-1 ${
                        competition.status === 'active' ? 'text-amber-400' : 'text-white/60'
                      }`}>
                        {competition.time_remaining}
                      </div>
                      <div className="text-white/50 text-xs">
                        {competition.status === 'active' ? 'Remaining' : 'Status'}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Clean Competition Tables */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
          {/* My Created Competitions */}
          <Card className="bg-white/[0.01] border-white/[0.06] hover:border-white/[0.08] transition-all duration-200 shadow-[0_1px_3px_rgba(0,0,0,0.12)]">
            <CardHeader className="pb-6 border-b border-white/[0.06]">
              <CardTitle className="text-white text-xl font-medium" style={{ fontFamily: 'Inter, system-ui, sans-serif', fontWeight: 500 }}>
                My Created Competitions
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="space-y-0">
                {allCreatedCompetitions.length > 0 ? (
                  allCreatedCompetitions.map((competition, index) => (
                    <div
                      key={competition.id}
                      className={`p-6 hover:bg-white/[0.03] transition-all duration-200 cursor-pointer group ${
                        index !== allCreatedCompetitions.length - 1 ? 'border-b border-white/[0.05]' : ''
                      }`}
                      onClick={() => setSelectedCompetition(competition)}
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-white font-semibold text-base group-hover:text-white/80 transition-colors" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                              {competition.name}
                            </h3>
                            <div className={`px-2 py-1 rounded-md text-xs font-medium ${
                              competition.status === 'active'
                                ? 'bg-[#22C55E]/[0.15] border border-[#22C55E]/[0.3] text-[#22C55E]'
                                : competition.status === 'completed'
                                ? 'bg-white/[0.08] border border-white/[0.15] text-white/60'
                                : 'bg-[#F43F5E]/[0.15] border border-[#F43F5E]/[0.3] text-[#F43F5E]'
                            }`}>
                              {competition.status}
                            </div>
                          </div>
                          <p className="text-white/50 text-sm mb-3 line-clamp-1">
                            {competition.description || 'No description available'}
                          </p>
                        </div>
                        <div className="text-right ml-4">
                          <div className="text-xl font-semibold text-white mb-1" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                            ${((competition as any).revenue || (competition.entry_fee || 0) * (competition.participant_count || 0)).toLocaleString()}
                          </div>
                          <div className="text-xs text-white/50 font-medium">Revenue</div>
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4">
                        <div className="bg-white/[0.03] rounded-lg p-3 text-center">
                          <div className="text-white font-semibold text-sm mb-1">{competition.participant_count || 0}</div>
                          <div className="text-white/50 text-xs">Participants</div>
                        </div>
                        <div className="bg-white/[0.03] rounded-lg p-3 text-center">
                          <div className="text-white font-semibold text-sm mb-1">${(competition.prize_pool || 0).toLocaleString()}</div>
                          <div className="text-white/50 text-xs">Prize Pool</div>
                        </div>
                        <div className="bg-white/[0.03] rounded-lg p-3 text-center">
                          <div className="text-white font-semibold text-sm mb-1">
                            {new Date(competition.competition_start).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                          </div>
                          <div className="text-white/50 text-xs">Start Date</div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-12 text-center">
                    <div className="w-16 h-16 bg-white/[0.05] rounded-full flex items-center justify-center mx-auto mb-4">
                      <Trophy className="w-8 h-8 text-white/20" />
                    </div>
                    <h3 className="text-white/60 text-base mb-2 font-medium" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                      No competitions yet
                    </h3>
                    <p className="text-white/40 text-sm">Create your first competition to start tracking analytics</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Top Performers */}
          <Card className="bg-white/[0.01] border-white/[0.06] hover:border-white/[0.08] transition-all duration-200 shadow-[0_1px_3px_rgba(0,0,0,0.12)]">
            <CardHeader className="pb-6 border-b border-white/[0.06]">
              <CardTitle className="text-white text-xl font-medium" style={{ fontFamily: 'Inter, system-ui, sans-serif', fontWeight: 500 }}>
                Top Performers
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                {[
                  { name: 'Alex Chen', profit: '+$12,450', rank: 1, change: '+24.5%', period: '7d' },
                  { name: 'Sarah Kim', profit: '+$8,920', rank: 2, change: '+18.2%', period: '7d' },
                  { name: 'Mike Johnson', profit: '+$7,340', rank: 3, change: '+15.8%', period: '7d' },
                  { name: 'Emma Davis', profit: '+$5,680', rank: 4, change: '+12.1%', period: '7d' },
                  { name: 'David Wilson', profit: '+$4,230', rank: 5, change: '+9.7%', period: '7d' }
                ].map((trader, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between py-3 border-b border-white/[0.04] last:border-b-0 hover:bg-white/[0.02] transition-all duration-200 group"
                  >
                    <div className="flex items-center gap-4">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                        trader.rank === 1
                          ? 'bg-amber-500/[0.15] text-amber-400'
                          : trader.rank === 2
                          ? 'bg-white/[0.1] text-white/70'
                          : trader.rank === 3
                          ? 'bg-orange-500/[0.15] text-orange-400'
                          : 'bg-white/[0.05] text-white/50'
                      }`} style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
                        {trader.rank}
                      </div>
                      <div>
                        <div className="text-white font-medium text-sm" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
                          {trader.name}
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          <div className="px-2 py-0.5 bg-emerald-500/[0.1] rounded text-xs text-emerald-400 font-medium">
                            {trader.change}
                          </div>
                          <span className="text-white/40 text-xs">{trader.period}</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-white font-medium text-sm" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
                        {trader.profit}
                      </div>
                      <div className="text-white/40 text-xs mt-1">Total Profit</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </motion.div>
  );
};

export default CompetitionAnalytics;
