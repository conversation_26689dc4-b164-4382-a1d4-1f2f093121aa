/**
 * Environment Configuration
 * Centralized access to all Vite environment variables
 * All variables must be prefixed with VITE_ to be accessible on the frontend
 */

// Supabase Configuration
export const SUPABASE_CONFIG = {
  url: import.meta.env.VITE_SUPABASE_URL,
  anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY,
} as const;

// Encryption Configuration
export const ENCRYPTION_CONFIG = {
  key: import.meta.env.VITE_ENCRYPTION_KEY,
} as const;

// Stripe Configuration
export const STRIPE_CONFIG = {
  publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY,
  livePriceId: import.meta.env.VITE_STRIPE_LIVE_PRICE_ID,
} as const;

// Growi Configuration
export const GROWI_CONFIG = {
  publicId: import.meta.env.VITE_GROWI_PUBLIC_ID,
} as const;

// Whop Configuration
export const WHOP_CONFIG = {
  // Intermediary server URLs
  intermediaryUrl: import.meta.env.VITE_WHOP_INTERMEDIARY_URL,
  intermediaryProductionUrl: import.meta.env.VITE_WHOP_INTERMEDIARY_PRODUCTION_URL,

  // OSIS App (main app)
  appId: import.meta.env.VITE_WHOP_APP_ID,
  agentUserId: import.meta.env.VITE_WHOP_AGENT_USER_ID,
  companyId: import.meta.env.VITE_WHOP_COMPANY_ID,

  // Trading App
  tradingAppId: import.meta.env.VITE_TRADING_WHOP_APP_ID,
  tradingAgentUserId: import.meta.env.VITE_TRADING_WHOP_AGENT_USER_ID,
  tradingCompanyId: import.meta.env.VITE_TRADING_WHOP_COMPANY_ID,

  // Next.js style variables for SDK compatibility
  nextPublicAppId: import.meta.env.VITE_NEXT_PUBLIC_WHOP_APP_ID,
} as const;

// API Keys (Frontend accessible)
export const API_KEYS = {
  polygon: import.meta.env.VITE_POLYGON_API_KEY,
  elevenlabs: import.meta.env.VITE_ELEVENLABS_API_KEY,
  financialDatasets: import.meta.env.VITE_FINANCIALDATASETS_API_KEY,
  gemini: import.meta.env.VITE_GEMINI_API_KEY,
  serp: import.meta.env.VITE_SERP_API_KEY,
  alphaVantage: import.meta.env.VITE_ALPHA_VANTAGE_API_KEY,
  anthropic: import.meta.env.VITE_ANTHROPIC_API_KEY,
  eva: import.meta.env.VITE_EVA_API_KEY,
  grok: import.meta.env.VITE_GROK_API_KEY,
  perplexity: import.meta.env.VITE_PERPLEXITY_API_KEY,
  youtube: import.meta.env.VITE_YOUTUBE_API_KEY,
} as const;

// PostHog Configuration
export const POSTHOG_CONFIG = {
  key: import.meta.env.VITE_POSTHOG_KEY,
  host: import.meta.env.VITE_POSTHOG_HOST,
} as const;

// Environment Detection
const isWhopProduction = () => {
  // Check if we're in Whop production environment
  const hostname = typeof window !== 'undefined' ? window.location.hostname : '';
  const isWhopDomain = hostname.includes('whop.com') || hostname.includes('whop.io');
  const isLocalhost = hostname.includes('localhost') || hostname.includes('127.0.0.1');

  // Enable production lock if:
  // 1. Explicitly set via environment variable, OR
  // 2. We're on a Whop domain (production), OR
  // 3. We're in production mode but NOT localhost
  return import.meta.env.VITE_PRODUCTION_LOCK === 'true' ||
         isWhopDomain ||
         (import.meta.env.PROD && !isLocalhost);
};

export const ENV = {
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,
  mode: import.meta.env.MODE,
  productionLock: isWhopProduction(),
} as const;

// Validation function to check if required environment variables are set
export const validateEnvironment = () => {
  const required = {
    'VITE_SUPABASE_URL': SUPABASE_CONFIG.url,
    'VITE_SUPABASE_ANON_KEY': SUPABASE_CONFIG.anonKey,
    'VITE_WHOP_INTERMEDIARY_URL': WHOP_CONFIG.intermediaryUrl,
    'VITE_STRIPE_PUBLISHABLE_KEY': STRIPE_CONFIG.publishableKey,
  };

  const missing = Object.entries(required)
    .filter(([, value]) => !value)
    .map(([key]) => key);

  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
};

// Export all configurations as a single object for convenience
export const CONFIG = {
  supabase: SUPABASE_CONFIG,
  encryption: ENCRYPTION_CONFIG,
  stripe: STRIPE_CONFIG,
  growi: GROWI_CONFIG,
  whop: WHOP_CONFIG,
  apiKeys: API_KEYS,
  posthog: POSTHOG_CONFIG,
  env: ENV,
} as const;

// Default export
export default CONFIG;
