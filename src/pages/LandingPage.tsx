import { useState, useEffect } from 'react';
import { AnimatePresence } from "framer-motion";
import { useNavigate } from 'react-router-dom';
import AuthModal from "@/components/auth/AuthModal";
import { useUnifiedAuth } from '@/hooks/useUnifiedAuth';

const LandingPage = () => {
  const [showAuthModal, setShowAuthModal] = useState(true);
  const { isAuthenticated, isWhopUser } = useUnifiedAuth();
  const navigate = useNavigate();

  // Debug logging for authentication state
  useEffect(() => {
    console.log('🔍 LandingPage Auth Debug:', {
      isAuthenticated,
      isWhopUser,
      timestamp: new Date().toISOString()
    });
  }, [isAuthenticated, isWhopUser]);

  // If user is already authenticated (including Whop users), redirect to home
  useEffect(() => {
    if (isAuthenticated) {
      console.log('🔄 LandingPage: User is authenticated, redirecting to home', {
        isWhopUser,
        timestamp: new Date().toISOString()
      });
      navigate('/home', { replace: true });
    }
  }, [isAuthenticated, isWhopUser, navigate]);

  // Don't show auth modal for Whop users
  const shouldShowAuthModal = showAuthModal && !isWhopUser;

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-[#0A0A0A] p-4">
      <AnimatePresence>
        {shouldShowAuthModal && (
          <AuthModal skipIntro={true} />
        )}
      </AnimatePresence>
    </div>
  );
};

export default LandingPage; 