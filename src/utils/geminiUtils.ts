import { trackLLMEvent } from './analytics';
import { encryptData, obfuscateData } from './cryptoUtils';
import { getValidAccessToken, handleAuthError } from './tokenUtils';

interface GeminiRequestConfig {
  contents: Array<{
    parts: Array<{
      text: string;
    }>;
  }>;
  generationConfig?: {
    temperature?: number;
    maxOutputTokens?: number;
    topK?: number;
    topP?: number;
  };
  tools?: Array<{
    googleSearch?: Record<string, never>;
  }>;
}

interface GeminiOptions {
  distinctId?: string;
  traceId?: string;
  properties?: Record<string, any>;
  groups?: Record<string, string>;
  privacyMode?: boolean;
  useGrounding?: boolean;
}

// Get the encryption key from environment variables
const ENCRYPTION_KEY = import.meta.env.VITE_ENCRYPTION_KEY || '';

/**
 * Process data through AI models
 * @param inputData - The input data to process
 * @param processingOptions - Options for processing
 * @param modelConfig - Configuration for the model
 * @returns Processed data from the model
 */
export async function callGeminiAPI(
  prompt: string,
  options: GeminiOptions = {},
  config: Partial<GeminiRequestConfig> = {}
) {
  const startTime = Date.now();

  try {
    // Get a valid access token using our token utility
    const accessToken = await getValidAccessToken();

    if (!accessToken) {
      throw new Error('No authentication token found. Please log in.');
    }

    // Encrypt the prompt and other sensitive data
    const encryptedPrompt = await encryptData(prompt, ENCRYPTION_KEY);

    // Create payload with encrypted data
    const payload = {
      data: encryptedPrompt,
      metadata: obfuscateData({
        options,
        config
      })
    };

    // Use the Supabase edge function with a generic name
    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/data-processor`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify(payload)
    });

    // Check for authentication errors
    if (response.status === 401) {
      console.warn('Authentication error in Gemini API call, attempting to refresh token...');
      // Try to refresh the token and retry the request
      const newToken = await getValidAccessToken();


    }

    const responseData = await response.json();
    const endTime = Date.now();
    const latency = (endTime - startTime) / 1000; // Convert to seconds

    // If the edge function returned an error, throw it
    if (responseData.error) {
      // Try to handle authentication errors
      const isAuthError = await handleAuthError({ message: responseData.error });

      if (isAuthError) {
        // Retry the request with a fresh token
        return await callGeminiAPI(prompt, options, config);
      }

      throw new Error(responseData.error);
    }

    const data = responseData.data;
    const outputText = data?.candidates?.[0]?.content?.parts?.[0]?.text || '';
    const groundingMetadata = data?.candidates?.[0]?.groundingMetadata;

    // Track the event in PostHog
    trackLLMEvent('gemini_api_call', {
      distinctId: options.distinctId,
      traceId: options.traceId,
      input: options.privacyMode ? undefined : prompt,
      output: options.privacyMode ? undefined : outputText,
      latency,
      properties: {
        http_status: response.status,
        is_error: !response.ok,
        error: !response.ok ? responseData.error : undefined,
        has_grounding: !!groundingMetadata,
        has_polygon_data: options.properties?.has_price_data || false,
        ...options.properties
      }
    });

    return data;
  } catch (error) {
    const endTime = Date.now();
    const latency = (endTime - startTime) / 1000;

    // Try to handle authentication errors
    const isAuthError = await handleAuthError(error);

    if (isAuthError) {
      // Retry the request
      return await callGeminiAPI(prompt, options, config);
    }

    // Track error event
    trackLLMEvent('gemini_api_error', {
      distinctId: options.distinctId,
      traceId: options.traceId,
      input: options.privacyMode ? undefined : prompt,
      latency,
      properties: {
        error: error.message,
        ...options.properties
      }
    });

    throw error;
  }
}