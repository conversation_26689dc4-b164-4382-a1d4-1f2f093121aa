/**
 * Development utilities for testing Whop integration
 * These functions help simulate Whop environment for development and testing
 */

import { storeWhopUserToken, clearWhopUserToken } from './whopAuth';

// Mock Whop user token for development
const MOCK_WHOP_TOKEN = 'mock-whop-token-for-development';

// Mock experience IDs for testing
export const MOCK_EXPERIENCE_IDS = {
  BASIC: 'exp_basic_123',
  PREMIUM: 'exp_premium_456',
  ADMIN: 'exp_admin_789'
};

/**
 * Enable Whop development mode
 * This simulates being in a Whop iframe environment
 */
export const enableWhopDevMode = (experienceId?: string) => {
  if (typeof window === 'undefined') return;

  // Console logging removed

  // Store mock token
  storeWhopUserToken(MOCK_WHOP_TOKEN);

  // Add Whop-specific URL parameters
  const url = new URL(window.location.href);
  url.searchParams.set('whop', 'true');
  url.searchParams.set('whop_dev', 'true');
  
  if (experienceId) {
    // Navigate to experience page
    window.history.pushState({}, '', `/experiences/${experienceId}?whop=true&whop_dev=true`);
  } else {
    // Just add query parameters
    window.history.pushState({}, '', url.toString());
  }

  // Simulate iframe environment
  Object.defineProperty(window, 'parent', {
    value: {
      ...window.parent,
      location: {
        hostname: 'whop.com'
      }
    },
    writable: false
  });

  // Console logging removed
};

/**
 * Disable Whop development mode
 */
export const disableWhopDevMode = () => {
  if (typeof window === 'undefined') return;

  // Console logging removed

  // Clear mock token
  clearWhopUserToken();

  // Remove Whop-specific URL parameters
  const url = new URL(window.location.href);
  url.searchParams.delete('whop');
  url.searchParams.delete('whop_dev');
  url.searchParams.delete('whop_token');

  // Navigate back to regular page if on experience page
  if (window.location.pathname.startsWith('/experiences/')) {
    window.history.pushState({}, '', '/');
  } else {
    window.history.pushState({}, '', url.toString());
  }

  // Console logging removed
};

/**
 * Check if we're in Whop development mode
 */
export const isWhopDevMode = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('whop_dev') === 'true';
};

/**
 * Get development tools for Whop integration
 */
export const getWhopDevTools = () => {
  return {
    enableWhopDevMode,
    disableWhopDevMode,
    isWhopDevMode: isWhopDevMode(),
    mockExperienceIds: MOCK_EXPERIENCE_IDS,

    // Quick access functions
    enableBasicExperience: () => enableWhopDevMode(MOCK_EXPERIENCE_IDS.BASIC),
    enablePremiumExperience: () => enableWhopDevMode(MOCK_EXPERIENCE_IDS.PREMIUM),
    enableAdminExperience: () => enableWhopDevMode(MOCK_EXPERIENCE_IDS.ADMIN),

    // Utility functions
    getCurrentExperienceId: () => {
      const match = window.location.pathname.match(/\/experiences\/([^\/]+)/);
      return match ? match[1] : null;
    },

    // API testing functions
    testApiConnection: async () => {
      try {
        // Console logging removed
        const response = await fetch('/api/whop/test-connection');
        const result = await response.json();
        // Console logging removed
        return result;
      } catch (error) {
        // Console logging removed
        return { success: false, error: error.message };
      }
    },

    testTokenVerification: async () => {
      try {
        // Console logging removed
        const token = localStorage.getItem('whop_user_token') || MOCK_WHOP_TOKEN;
        const response = await fetch('/api/whop/verify-user', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-whop-user-token': token
          }
        });
        const result = await response.json();
        // Console logging removed
        return result;
      } catch (error) {
        // Console logging removed
        return { success: false, error: error.message };
      }
    },

    // Console helpers
    logWhopStatus: () => {
      // Console logging removed
    }
  };
};

/**
 * Initialize Whop development tools in global scope for easy access
 */
export const initWhopDevTools = () => {
  if (typeof window === 'undefined' || import.meta.env.PROD) return;

  // Add to global scope for console access
  (window as any).whopDev = getWhopDevTools();

  // Console logging removed
};

// Auto-initialize in development
if (import.meta.env.DEV) {
  initWhopDevTools();
}
