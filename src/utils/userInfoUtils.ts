import { supabase } from '@/integrations/supabase/client';
import { getCurrentAuthenticatedUser } from './authUtils';
import { isCurrentUserAdmin } from '@/services/adminService';

export interface UserInfoData {
  // Regular user info
  user: {
    id: string;
    email: string | null;
    user_metadata: any;
    isWhopUser: boolean;
  } | null;

  // Whop user info
  whopUser: {
    id: string;
    username: string;
    email: string;
    profilePicUrl?: string;
  } | null;

  // Whop owner/business info
  whopOwnerInfo: {
    isWhopOwner: boolean;
    accessLevel: string | null;
    companyId: string | null;
    businessId: string | null;
    businessHandle: string | null;
    isOfficialOsis: boolean;
  };

  // Admin status
  adminInfo: {
    isAdmin: boolean;
    adminEmails: string[];
  };

  // Access info
  accessInfo: {
    hasAccess: boolean;
    accessLevel: string;
    experienceId: string | null;
  };
}

/**
 * Gather comprehensive user information including Whop owner status
 */
export const gatherUserInfo = async (): Promise<UserInfoData> => {
  try {
    // Get current authenticated user
    const user = await getCurrentAuthenticatedUser();
    
    // Check admin status
    const isAdmin = await isCurrentUserAdmin();
    const adminEmails = ['<EMAIL>', '<EMAIL>'];

    // Initialize default data
    const userInfo: UserInfoData = {
      user,
      whopUser: null,
      whopOwnerInfo: {
        isWhopOwner: false,
        accessLevel: null,
        companyId: null,
        businessId: null,
        businessHandle: null,
        isOfficialOsis: false,
      },
      adminInfo: {
        isAdmin,
        adminEmails,
      },
      accessInfo: {
        hasAccess: false,
        accessLevel: 'no_access',
        experienceId: null,
      },
    };

    if (!user) {
      return userInfo;
    }

    // Extract Whop information from user metadata
    const metadata = user.user_metadata || {};
    
    // Set Whop owner info from metadata
    userInfo.whopOwnerInfo = {
      isWhopOwner: metadata.whop_access_level === 'admin',
      accessLevel: metadata.whop_access_level || null,
      companyId: metadata.whop_company_id || null,
      businessId: metadata.whop_business_id || null,
      businessHandle: metadata.whop_business_handle || null,
      isOfficialOsis: metadata.whop_business_id === 'biz_OGyv6Pz0Le35Fa' || 
                     metadata.whop_business_handle === 'tryosis',
    };

    // If this is a Whop user, extract Whop user info
    if (user.isWhopUser && metadata.whop_user_id) {
      userInfo.whopUser = {
        id: metadata.whop_user_id,
        username: metadata.username || 'Unknown',
        email: user.email || '',
        profilePicUrl: metadata.avatar_url,
      };

      // Set access info for Whop users
      userInfo.accessInfo = {
        hasAccess: true, // Whop users generally have access
        accessLevel: metadata.whop_access_level || 'customer',
        experienceId: metadata.whop_experience_id || null,
      };
    }

    return userInfo;
  } catch (error) {
    console.error('Error gathering user info:', error);
    
    // Return default data on error
    return {
      user: null,
      whopUser: null,
      whopOwnerInfo: {
        isWhopOwner: false,
        accessLevel: null,
        companyId: null,
        businessId: null,
        businessHandle: null,
        isOfficialOsis: false,
      },
      adminInfo: {
        isAdmin: false,
        adminEmails: ['<EMAIL>', '<EMAIL>'],
      },
      accessInfo: {
        hasAccess: false,
        accessLevel: 'no_access',
        experienceId: null,
      },
    };
  }
};

/**
 * Print comprehensive user information to console
 */
export const printUserInfo = async (): Promise<void> => {
  const userInfo = await gatherUserInfo();
  
  console.log('='.repeat(60));
  console.log('🔍 COMPREHENSIVE USER INFORMATION');
  console.log('='.repeat(60));
  
  // Regular User Info
  console.log('\n👤 REGULAR USER INFO:');
  if (userInfo.user) {
    console.log(`  ID: ${userInfo.user.id}`);
    console.log(`  Email: ${userInfo.user.email || 'N/A'}`);
    console.log(`  Is Whop User: ${userInfo.user.isWhopUser}`);
    console.log(`  Metadata:`, userInfo.user.user_metadata);
  } else {
    console.log('  No user found');
  }

  // Whop User Info
  console.log('\n🏪 WHOP USER INFO:');
  if (userInfo.whopUser) {
    console.log(`  Whop ID: ${userInfo.whopUser.id}`);
    console.log(`  Username: ${userInfo.whopUser.username}`);
    console.log(`  Email: ${userInfo.whopUser.email}`);
    console.log(`  Profile Pic: ${userInfo.whopUser.profilePicUrl || 'N/A'}`);
  } else {
    console.log('  Not a Whop user');
  }

  // Whop Owner Info
  console.log('\n👑 WHOP OWNER INFO:');
  console.log(`  Is Whop Owner: ${userInfo.whopOwnerInfo.isWhopOwner}`);
  console.log(`  Access Level: ${userInfo.whopOwnerInfo.accessLevel || 'N/A'}`);
  console.log(`  Company ID: ${userInfo.whopOwnerInfo.companyId || 'N/A'}`);
  console.log(`  Business ID: ${userInfo.whopOwnerInfo.businessId || 'N/A'}`);
  console.log(`  Business Handle: ${userInfo.whopOwnerInfo.businessHandle || 'N/A'}`);
  console.log(`  Is Official Osis: ${userInfo.whopOwnerInfo.isOfficialOsis}`);

  // Admin Info
  console.log('\n🛡️ ADMIN INFO:');
  console.log(`  Is Admin: ${userInfo.adminInfo.isAdmin}`);
  console.log(`  Admin Emails: ${userInfo.adminInfo.adminEmails.join(', ')}`);

  // Access Info
  console.log('\n🔐 ACCESS INFO:');
  console.log(`  Has Access: ${userInfo.accessInfo.hasAccess}`);
  console.log(`  Access Level: ${userInfo.accessInfo.accessLevel}`);
  console.log(`  Experience ID: ${userInfo.accessInfo.experienceId || 'N/A'}`);

  console.log('\n' + '='.repeat(60));
};

/**
 * Expose user info functions globally for easy debugging
 */
if (typeof window !== 'undefined') {
  (window as any).printUserInfo = printUserInfo;
  (window as any).gatherUserInfo = gatherUserInfo;
}
