/**
 * Debug utilities for settings page to help identify freezing issues
 */

export const debugSettings = {
  /**
   * Monitor save operations for performance issues
   */
  monitorSaveOperation: (operationName: string) => {
    const startTime = performance.now();
    // Console logging removed

    return {
      end: (success: boolean = true) => {
        const endTime = performance.now();
        const duration = endTime - startTime;

        // Console logging removed

        return duration;
      }
    };
  },

  /**
   * Log component mount/unmount cycles
   */
  logComponentLifecycle: (componentName: string, action: 'mount' | 'unmount') => {
    // Console logging removed
  },

  /**
   * Monitor API calls for hanging requests
   */
  monitorApiCall: async <T>(
    apiCall: () => Promise<T>, 
    callName: string, 
    timeoutMs: number = 10000
  ): Promise<T> => {
    const monitor = debugSettings.monitorSaveOperation(`API Call: ${callName}`);
    
    try {
      const timeoutPromise = new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error(`${callName} timed out after ${timeoutMs}ms`)), timeoutMs)
      );

      const result = await Promise.race([apiCall(), timeoutPromise]);
      monitor.end(true);
      return result;
    } catch (error) {
      monitor.end(false);
      throw error;
    }
  },

  /**
   * Check for memory leaks in settings components
   */
  checkMemoryUsage: () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      // Console logging removed
    }
  },

  /**
   * Log state changes that might cause re-renders
   */
  logStateChange: (stateName: string, oldValue: any, newValue: any) => {
    if (oldValue !== newValue) {
      // Console logging removed
    }
  }
};

// Export for use in components during development
export default debugSettings;
