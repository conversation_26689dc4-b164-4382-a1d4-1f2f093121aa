/**
 * Performance Utilities
 * Collection of utilities for optimizing React components and application performance
 */

import { useCallback, useMemo, useRef, useEffect, useState } from 'react';

// Debounce function for performance optimization
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null;
  
  return (...args: Parameters<T>) => {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
};

// Throttle function for performance optimization
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// Memoization utility with custom equality check
export const memoizeWithCustomEquality = <T extends (...args: any[]) => any>(
  fn: T,
  isEqual: (a: Parameters<T>, b: Parameters<T>) => boolean
): T => {
  const cache = new Map();
  
  return ((...args: Parameters<T>) => {
    for (const [cachedArgs, cachedResult] of cache.entries()) {
      if (isEqual(args, cachedArgs)) {
        return cachedResult;
      }
    }
    
    const result = fn(...args);
    cache.set(args, result);
    
    // Limit cache size to prevent memory leaks
    if (cache.size > 100) {
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }
    
    return result;
  }) as T;
};

// Deep equality check for objects
export const deepEqual = (a: any, b: any): boolean => {
  if (a === b) return true;
  
  if (a == null || b == null) return false;
  
  if (typeof a !== typeof b) return false;
  
  if (typeof a !== 'object') return false;
  
  const keysA = Object.keys(a);
  const keysB = Object.keys(b);
  
  if (keysA.length !== keysB.length) return false;
  
  for (const key of keysA) {
    if (!keysB.includes(key)) return false;
    if (!deepEqual(a[key], b[key])) return false;
  }
  
  return true;
};

// Shallow equality check for objects
export const shallowEqual = (a: any, b: any): boolean => {
  if (a === b) return true;
  
  if (a == null || b == null) return false;
  
  if (typeof a !== 'object' || typeof b !== 'object') return false;
  
  const keysA = Object.keys(a);
  const keysB = Object.keys(b);
  
  if (keysA.length !== keysB.length) return false;
  
  for (const key of keysA) {
    if (a[key] !== b[key]) return false;
  }
  
  return true;
};

// Hook for debounced values
export const useDebouncedValue = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  
  return debouncedValue;
};

// Hook for throttled callbacks
export const useThrottledCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const throttledCallback = useMemo(
    () => throttle(callback, delay),
    [callback, delay]
  );
  
  return throttledCallback as T;
};

// Hook for debounced callbacks
export const useDebouncedCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const debouncedCallback = useMemo(
    () => debounce(callback, delay),
    [callback, delay]
  );
  
  return debouncedCallback as T;
};

// Hook for memoized expensive calculations
export const useExpensiveCalculation = <T>(
  calculate: () => T,
  dependencies: any[]
): T => {
  return useMemo(calculate, dependencies);
};

// Hook for stable callback references
export const useStableCallback = <T extends (...args: any[]) => any>(
  callback: T
): T => {
  const callbackRef = useRef(callback);
  
  useEffect(() => {
    callbackRef.current = callback;
  });
  
  return useCallback((...args: Parameters<T>) => {
    return callbackRef.current(...args);
  }, []) as T;
};

// Hook for intersection observer (lazy loading)
export const useIntersectionObserver = (
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);
  
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );
    
    observer.observe(element);
    
    return () => observer.disconnect();
  }, [elementRef, options, hasIntersected]);
  
  return { isIntersecting, hasIntersected };
};

// Hook for measuring component render time
export const useRenderTime = (componentName: string) => {
  const renderStart = useRef<number>(0);
  
  useEffect(() => {
    renderStart.current = performance.now();
  });
  
  useEffect(() => {
    const renderTime = performance.now() - renderStart.current;
    
    if (process.env.NODE_ENV === 'development') {
      // Console logging removed
    }

    // Log slow renders
    if (renderTime > 16) { // 60fps = 16.67ms per frame
      // Console logging removed
    }
  });
};

// Virtual scrolling utility
export const useVirtualScrolling = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan = 5
) => {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleRange = useMemo(() => {
    const start = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const end = Math.min(
      items.length,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );
    
    return { start, end };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);
  
  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.start, visibleRange.end).map((item, index) => ({
      item,
      index: visibleRange.start + index,
    }));
  }, [items, visibleRange]);
  
  const totalHeight = items.length * itemHeight;
  const offsetY = visibleRange.start * itemHeight;
  
  return {
    visibleItems,
    totalHeight,
    offsetY,
    setScrollTop,
  };
};

// Performance measurement utilities
export const measurePerformance = {
  // Measure function execution time
  time: <T extends (...args: any[]) => any>(fn: T, name?: string): T => {
    return ((...args: Parameters<T>) => {
      const start = performance.now();
      const result = fn(...args);
      const end = performance.now();
      
      const functionName = name || fn.name || 'anonymous';
      // Console logging removed
      
      return result;
    }) as T;
  },
  
  // Measure async function execution time
  timeAsync: <T extends (...args: any[]) => Promise<any>>(fn: T, name?: string): T => {
    return (async (...args: Parameters<T>) => {
      const start = performance.now();
      const result = await fn(...args);
      const end = performance.now();

      const functionName = name || fn.name || 'anonymous';

      return result;
    }) as T;
  },
  
  // Mark performance milestones
  mark: (name: string) => {
    if ('performance' in window && 'mark' in performance) {
      performance.mark(name);
    }
  },
  
  // Measure between two marks
  measure: (name: string, startMark: string, endMark?: string) => {
    if ('performance' in window && 'measure' in performance) {
      try {
        performance.measure(name, startMark, endMark);
        const measure = performance.getEntriesByName(name, 'measure')[0];
        // Console logging removed
        return measure.duration;
      } catch (error) {
        // Console logging removed
        return 0;
      }
    }
    return 0;
  },
};

// Bundle size analysis utilities
export const bundleAnalysis = {
  // Analyze chunk sizes
  analyzeChunks: () => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const jsResources = resources.filter(resource => 
        resource.name.includes('.js') && resource.transferSize
      );
      
      const totalSize = jsResources.reduce((sum, resource) => sum + resource.transferSize, 0);

      jsResources
        .sort((a, b) => b.transferSize - a.transferSize)
        .slice(0, 10)
        .forEach(resource => {
          const name = resource.name.split('/').pop() || resource.name;
        });
    }
  },
  
  // Monitor resource loading
  monitorResources: () => {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          const resource = entry as PerformanceResourceTiming;
          if (resource.transferSize > 100 * 1024) { // > 100KB
            // Console logging removed
          }
        });
      });
      
      observer.observe({ entryTypes: ['resource'] });
      
      // Cleanup after 30 seconds
      setTimeout(() => observer.disconnect(), 30000);
    }
  },
};

// Initialize performance monitoring in development
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  // Monitor bundle sizes
  window.addEventListener('load', () => {
    setTimeout(() => {
      bundleAnalysis.analyzeChunks();
      bundleAnalysis.monitorResources();
    }, 1000);
  });
}
