import posthog from 'posthog-js';

// Initialize PostHog
const initPostHog = () => {
  const POSTHOG_KEY = import.meta.env.POSTHOG_KEY || import.meta.env.VITE_POSTHOG_KEY;
  const POSTHOG_HOST = import.meta.env.POSTHOG_HOST || import.meta.env.VITE_POSTHOG_HOST || 'https://us.i.posthog.com';

  if (!POSTHOG_KEY) {
    return;
  }

  posthog.init(POSTHOG_KEY, {
    api_host: POSTHOG_HOST,
    defaults: '2025-05-24',
    person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
    autocapture: true,
    capture_pageview: true,
    capture_pageleave: true,
    debug: import.meta.env.DEV || process.env.NODE_ENV !== 'production'
  });

  console.log('PostHog initialized with key:', POSTHOG_KEY.substring(0, 10) + '...');
};

// Track LLM events
export const trackLLMEvent = (
  eventName: string,
  {
    distinctId,
    traceId,
    input,
    output,
    model = 'gemini-2.0-flash',
    provider = 'google',
    latency,
    properties = {}
  }: {
    distinctId?: string;
    traceId?: string;
    input?: string;
    output?: string;
    model?: string;
    provider?: string;
    latency?: number;
    properties?: Record<string, any>;
  }
) => {
  // Ensure PostHog is initialized
  if (!posthog.__loaded) {
    initPostHog();
  }

  // Calculate tokens (rough estimate)
  const inputTokens = input ? Math.ceil(input.length / 4) : 0;
  const outputTokens = output ? Math.ceil(output.length / 4) : 0;

  // Send event to PostHog
  posthog.capture('$ai_generation', {
    distinct_id: distinctId || 'anonymous',
    $ai_trace_id: traceId || crypto.randomUUID(),
    $ai_model: model,
    $ai_provider: provider,
    $ai_input: input,
    $ai_input_tokens: inputTokens,
    $ai_output_choices: output ? [{ text: output }] : undefined,
    $ai_output_tokens: outputTokens,
    $ai_latency: latency,
    ...properties
  });

  // Log in development
  if (process.env.NODE_ENV !== 'production') {

  }
};

// Test PostHog functionality
export const testPostHog = () => {
  if (typeof window !== 'undefined' && window.posthog) {
    console.log('PostHog is available on window object');
    window.posthog.capture('posthog_test_event', {
      test: true,
      timestamp: new Date().toISOString(),
      source: 'analytics_test'
    });
    console.log('Test event sent to PostHog');
    return true;
  } else if (posthog && posthog.__loaded) {
    console.log('PostHog is available via import');
    posthog.capture('posthog_test_event', {
      test: true,
      timestamp: new Date().toISOString(),
      source: 'analytics_test'
    });
    console.log('Test event sent to PostHog');
    return true;
  } else {
    console.warn('PostHog is not available or not loaded');
    return false;
  }
};

// Export initialization function
export const initAnalytics = initPostHog;

// Export PostHog instance for direct access if needed
export { posthog };
