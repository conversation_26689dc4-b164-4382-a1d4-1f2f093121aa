/**
 * Growi script loader utility
 * Dynamically loads the Growi tracking script with the correct environment variable
 */

let growiLoaded = false;

/**
 * Loads the Growi tracking script if not already loaded
 */
export const loadGrowiScript = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Check if already loaded
    const existingScript = document.querySelector('script[src*="growi.js"]');
    if (growiLoaded || existingScript) {
      resolve();
      return;
    }

    // Remove any existing Growi scripts that might be malformed
    const allGrowiScripts = document.querySelectorAll('script[src*="growi.js"]');
    allGrowiScripts.forEach(script => {
      script.remove();
    });

    // Get the Growi Public ID from environment variables
    let growiPublicId = import.meta.env.VITE_GROWI_PUBLIC_ID;

    // Skip Growi loading if no public ID is configured
    if (!growiPublicId) {
      console.warn('⚠️ VITE_GROWI_PUBLIC_ID not configured, skipping Growi tracking');
      resolve(); // Resolve successfully but don't load the script
      return;
    }

    // Console logging removed

    // Create and load the script
    const script = document.createElement('script');
    script.src = 'https://cdn.growi.io/growi.js';
    script.async = true;
    script.setAttribute('data-growi-id', growiPublicId);

    // Console logging removed

    script.onload = () => {
      growiLoaded = true;
      // Console logging removed
      resolve();
    };

    script.onerror = (error) => {
      console.error('❌ Failed to load Growi script:', error);
      reject(error);
    };

    // Add to document head
    document.head.appendChild(script);
  });
};

/**
 * Initialize Growi tracking
 * Call this early in your app initialization
 */
export const initializeGrowiTracking = async (): Promise<void> => {
  try {
    await loadGrowiScript();
  } catch (error) {
    console.error('Failed to initialize Growi tracking:', error);
  }
};
