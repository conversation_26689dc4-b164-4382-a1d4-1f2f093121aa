import { useAuth } from '@/contexts/AuthContext';
import { useWhop } from '@/contexts/WhopContext';

/**
 * Unified authentication hook that combines regular auth and Whop auth
 * This provides a single interface for checking authentication status
 * regardless of whether the user is a regular user or a Whop user
 */
export const useUnifiedAuth = () => {
  const { isAuthenticated: isRegularAuth, user: regularUser, signOut: regularSignOut } = useAuth();
  const { isWhopUser, whopUser, accessResult } = useWhop();

  // Determine overall authentication status
  // For Whop users, if they have valid user data, they are authenticated
  // All Whop users should have access to the experience
  const whopAuthenticated = isWhopUser && !!whopUser;
  const isAuthenticated = isRegularAuth || whopAuthenticated;

  // Enhanced debug logging for development
  if (import.meta.env.DEV) {
    console.group('🔐 Unified Auth System Debug');
    console.log('📊 Authentication States:', {
      isRegularAuth,
      isWhopUser,
      hasWhopUser: !!whopUser,
      whopAuthenticated,
      finalAuthenticated: isAuthenticated,
      timestamp: new Date().toISOString()
    });

    console.log('👤 Regular User Info:', {
      hasRegularUser: !!regularUser,
      regularUserId: regularUser?.id,
      regularUserEmail: regularUser?.email,
      regularUserMetadata: regularUser?.user_metadata
    });

    console.log('🏢 Whop User Info:', {
      whopUserId: whopUser?.id,
      whopUsername: whopUser?.username,
      whopEmail: whopUser?.email,
      whopProfilePic: whopUser?.profilePicUrl
    });

    console.log('🔑 Access Result:', {
      hasAccessResult: !!accessResult,
      hasAccess: accessResult?.hasAccess,
      accessLevel: accessResult?.accessLevel,
      fullAccessResult: accessResult
    });

    console.groupEnd();
  }

  // Determine the current user (prefer regular user, then Whop user)
  const user = regularUser || (whopAuthenticated ? {
    id: whopUser?.id,
    email: whopUser?.email,
    user_metadata: {
      username: whopUser?.username,
      avatar_url: whopUser?.profilePicUrl,
      isWhopUser: true,
      whopAccessLevel: accessResult?.accessLevel
    }
  } : null);

  // Determine user type
  const userType = regularUser ? 'regular' : (isWhopUser ? 'whop' : 'none');

  // Sign out function that handles both types
  const signOut = async () => {
    if (regularUser) {
      await regularSignOut();
    }
    // For Whop users, we can't really "sign out" since they're in an iframe
    // But we can clear any local state if needed
    if (isWhopUser) {
      console.log('🔄 Whop user sign out requested - clearing local state');
      // Could clear local storage or redirect to Whop
    }
  };

  // Check if user has premium access (either through regular subscription or Whop)
  const hasPremiumAccess = () => {
    if (whopAuthenticated) {
      return true; // Authenticated Whop users are considered premium
    }

    // For regular users, check their subscription status
    // This would need to be implemented based on your existing subscription logic
    return isRegularAuth; // Simplified for now
  };

  return {
    // Authentication status
    isAuthenticated,
    user,
    userType,
    
    // User type checks
    isRegularUser: !!regularUser,
    isWhopUser: whopAuthenticated,
    
    // Whop-specific info
    whopAccessLevel: accessResult?.accessLevel,
    isWhopAdmin: accessResult?.accessLevel === 'admin',
    isWhopCustomer: accessResult?.accessLevel === 'customer',
    
    // Actions
    signOut,
    hasPremiumAccess,
    
    // Raw data for advanced use cases
    regularAuth: { isAuthenticated: isRegularAuth, user: regularUser },
    whopAuth: { isWhopUser, user: whopUser, accessResult }
  };
};

/**
 * Hook specifically for checking if user should skip authentication flows
 * Useful for components that need to know if they should show login prompts
 */
export const useAuthenticationStatus = () => {
  const { isAuthenticated, userType, isWhopUser } = useUnifiedAuth();
  
  return {
    isAuthenticated,
    shouldShowAuthPrompt: !isAuthenticated,
    shouldSkipGoogleLogin: isWhopUser, // Skip Google login for Whop users
    userType,
    authMethod: isWhopUser ? 'whop' : 'regular'
  };
};

/**
 * Hook for checking subscription/access status
 */
export const useAccessControl = () => {
  const { hasPremiumAccess, isWhopUser, whopAccessLevel, isAuthenticated } = useUnifiedAuth();
  
  return {
    hasAccess: hasPremiumAccess(),
    accessType: isWhopUser ? 'whop' : 'subscription',
    accessLevel: whopAccessLevel || 'customer',
    isAuthenticated,
    needsUpgrade: isAuthenticated && !hasPremiumAccess()
  };
};
