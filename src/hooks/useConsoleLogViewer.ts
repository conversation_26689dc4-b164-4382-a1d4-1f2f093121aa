import { useState, useEffect, useCallback } from 'react';

export const useConsoleLogViewer = () => {
  const [isVisible, setIsVisible] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('consoleViewer.isVisible');
      return saved ? JSON.parse(saved) : false;
    }
    return false;
  });

  const toggleVisibility = useCallback(() => {
    setIsVisible(prev => {
      const newValue = !prev;
      localStorage.setItem('consoleViewer.isVisible', JSON.stringify(newValue));
      return newValue;
    });
  }, []);

  const showConsole = useCallback(() => {
    setIsVisible(true);
    localStorage.setItem('consoleViewer.isVisible', JSON.stringify(true));
  }, []);

  const hideConsole = useCallback(() => {
    setIsVisible(false);
    localStorage.setItem('consoleViewer.isVisible', JSON.stringify(false));
  }, []);

  // Keyboard shortcut handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + C to toggle console
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'C') {
        event.preventDefault();
        toggleVisibility();
      }
      
      // F12 to toggle console (alternative shortcut)
      if (event.key === 'F12') {
        event.preventDefault();
        toggleVisibility();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [toggleVisibility]);

  // Expose functions globally for easy access from browser console
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).showConsoleViewer = showConsole;
      (window as any).hideConsoleViewer = hideConsole;
      (window as any).toggleConsoleViewer = toggleVisibility;
    }
  }, [showConsole, hideConsole, toggleVisibility]);

  return {
    isVisible,
    toggleVisibility,
    showConsole,
    hideConsole,
  };
};
