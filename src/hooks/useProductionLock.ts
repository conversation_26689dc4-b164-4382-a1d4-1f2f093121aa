import { ENV } from '@/config/env';
import { useState, useEffect } from 'react';

interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

/**
 * Hook to check if features should be locked in production
 * Returns true if production lock is enabled
 */
export const useProductionLock = () => {
  const isLocked = ENV.productionLock;

  // Debug logging for production lock status
  if (typeof window !== 'undefined') {
    console.log('🔒 Production Lock Status:', {
      isLocked,
      hostname: window.location.hostname,
      mode: ENV.mode,
      isDev: ENV.isDevelopment,
      isProd: ENV.isProduction,
      envVar: import.meta.env.VITE_PRODUCTION_LOCK
    });
  }

  return isLocked;
};

/**
 * Hook to get competition countdown for locked features
 */
export const useCompetitionCountdown = () => {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining>({ days: 0, hours: 0, minutes: 0, seconds: 0 });

  // Set competition start date - August 11th, 2025 at 12:00 PM EST
  const competitionStartDate = new Date('2025-08-11T16:00:00Z'); // 12:00 PM EST = 16:00 UTC (EDT in August)

  const calculateTimeRemaining = (targetDate: Date): TimeRemaining => {
    const now = new Date().getTime();
    const target = targetDate.getTime();
    const difference = target - now;

    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }

    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    return { days, hours, minutes, seconds };
  };

  useEffect(() => {
    // Calculate initial time remaining
    const initialTime = calculateTimeRemaining(competitionStartDate);
    setTimeRemaining(initialTime);

    const interval = setInterval(() => {
      const newTime = calculateTimeRemaining(competitionStartDate);
      setTimeRemaining(newTime);
    }, 1000);

    return () => clearInterval(interval);
  }, [competitionStartDate]);

  const formatTimeRemaining = () => {
    if (timeRemaining.days > 0) {
      return `${timeRemaining.days}d ${timeRemaining.hours}h ${timeRemaining.minutes}m`;
    } else if (timeRemaining.hours > 0) {
      return `${timeRemaining.hours}h ${timeRemaining.minutes}m`;
    } else if (timeRemaining.minutes > 0) {
      return `${timeRemaining.minutes}m ${timeRemaining.seconds}s`;
    } else {
      return `${timeRemaining.seconds}s`;
    }
  };

  return {
    timeRemaining,
    formattedTime: formatTimeRemaining(),
    hasTimeLeft: timeRemaining.days > 0 || timeRemaining.hours > 0 || timeRemaining.minutes > 0 || timeRemaining.seconds > 0
  };
};

/**
 * Hook to check if a specific feature is available
 * @param feature - The feature to check
 * @returns true if the feature is available (not locked)
 */
export const useFeatureAvailable = (feature: 'competitions' | 'analytics' | 'create-competition' | 'trading') => {
  const isLocked = useProductionLock();
  
  // If production lock is disabled, all features are available
  if (!isLocked) {
    return true;
  }
  
  // In production lock mode, only chart/trading features are available
  switch (feature) {
    case 'trading':
      return true; // Trading/chart is always available
    case 'competitions':
    case 'analytics':
    case 'create-competition':
      return false; // These are locked in production
    default:
      return false;
  }
};

/**
 * Get disabled props for buttons/elements when locked
 */
export const useProductionLockProps = (feature: 'competitions' | 'analytics' | 'create-competition' | 'trading') => {
  const isAvailable = useFeatureAvailable(feature);
  const { formattedTime, hasTimeLeft } = useCompetitionCountdown();

  const getTooltipMessage = () => {
    if (isAvailable) return undefined;

    // Always show countdown if feature is locked
    return `Available in: ${formattedTime}`;
  };

  return {
    disabled: !isAvailable,
    style: !isAvailable ? {
      opacity: 0.5,
      cursor: 'not-allowed'
    } : {},
    'data-tooltip': getTooltipMessage(),
    onClick: !isAvailable ? (e: any) => {
      e.preventDefault();
      e.stopPropagation();
      return false;
    } : undefined
  };
};
