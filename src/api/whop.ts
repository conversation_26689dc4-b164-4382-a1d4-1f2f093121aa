/**
 * Whop API integration handlers
 * These functions handle server-side Whop operations
 */

import { whopSdk } from '@/lib/whop-sdk';

export interface WhopApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Verify a Whop user token and return user information
 */
export const verifyWhopUser = async (token: string): Promise<WhopApiResponse> => {
  try {
    // Verify the token using Whop SDK
    const { userId } = await whopSdk.verifyUserToken({ token });

    if (!userId) {
      return {
        success: false,
        error: 'Invalid token - no user ID found'
      };
    }

    // Get user information
    const user = await whopSdk.users.getUser({ userId });

    if (!user) {
      return {
        success: false,
        error: 'User not found'
      };
    }

    return {
      success: true,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        profilePicUrl: user.profilePicUrl
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Check if a user has access to a specific experience
 */
export const checkWhopUserAccess = async (
  userId: string,
  experienceId: string
): Promise<WhopApiResponse> => {
  try {
    const result = await whopSdk.access.checkIfUserHasAccessToExperience({
      userId,
      experienceId,
    });

    return {
      success: true,
      data: {
        hasAccess: result.hasAccess,
        accessLevel: result.accessLevel,
        userId,
        experienceId
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Get user's experiences
 */
export const getUserExperiences = async (userId: string): Promise<WhopApiResponse> => {
  try {
    // This would depend on the specific Whop SDK methods available
    // For now, we'll return a placeholder response
    const experiences = await whopSdk.users.getUserExperiences?.({ userId }) || [];

    return {
      success: true,
      data: experiences
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Handle Whop webhook events
 */
export const handleWhopWebhook = async (
  event: string,
  data: any
): Promise<WhopApiResponse> => {
  try {
    switch (event) {
      case 'user.access_granted':
        // Handle when user gains access to an experience
        break;

      case 'user.access_revoked':
        // Handle when user loses access to an experience
        break;

      case 'experience.created':
        // Handle when a new experience is created
        break;

      default:
    }

    return {
      success: true,
      data: { event, processed: true }
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Get Whop app configuration
 */
export const getWhopAppConfig = (): WhopApiResponse => {
  try {
    const config = {
      appId: import.meta.env.VITE_WHOP_APP_ID,
      agentUserId: import.meta.env.VITE_WHOP_AGENT_USER_ID,
      companyId: import.meta.env.VITE_WHOP_COMPANY_ID,
      hasApiKey: !!import.meta.env.WHOP_API_KEY
    };

    return {
      success: true,
      data: config
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Test Whop connection
 */
export const testWhopConnection = async (): Promise<WhopApiResponse> => {
  try {
    // Try to get current user (using agent user ID)
    const agentUserId = import.meta.env.VITE_WHOP_AGENT_USER_ID;
    
    if (!agentUserId) {
      return {
        success: false,
        error: 'No agent user ID configured'
      };
    }

    const user = await whopSdk.users.getUser({ userId: agentUserId });

    if (!user) {
      return {
        success: false,
        error: 'Could not fetch agent user'
      };
    }

    return {
      success: true,
      data: {
        connected: true,
        agentUser: {
          id: user.id,
          username: user.username
        }
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Connection failed'
    };
  }
};
